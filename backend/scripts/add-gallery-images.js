import mongoose from 'mongoose';
import Product from '../src/shared/models/productModel.js';
import Category from '../src/shared/models/categoryModel.js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/ecommerce';

// Sample gallery images for different products
const sampleGalleryImages = [
  {
    image_url: '/uploads/products/kurti-detail-1.jpg',
    alt_text: 'Kurti fabric close-up',
    image_type: 'detail',
    sort_order: 1
  },
  {
    image_url: '/uploads/products/kurti-model-1.jpg',
    alt_text: 'Model wearing kurti',
    image_type: 'model',
    sort_order: 2
  },
  {
    image_url: '/uploads/products/kurti-back-view.jpg',
    alt_text: 'Kurti back view',
    image_type: 'gallery',
    sort_order: 3
  },
  {
    image_url: '/uploads/products/kurti-side-view.jpg',
    alt_text: 'Kurti side view',
    image_type: 'gallery',
    sort_order: 4
  }
];

const sampleLehengaGallery = [
  {
    image_url: '/uploads/products/lehenga-detail-1.jpg',
    alt_text: 'Lehenga embroidery detail',
    image_type: 'detail',
    sort_order: 1
  },
  {
    image_url: '/uploads/products/lehenga-model-1.jpg',
    alt_text: 'Model wearing lehenga',
    image_type: 'model',
    sort_order: 2
  },
  {
    image_url: '/uploads/products/lehenga-back-view.jpg',
    alt_text: 'Lehenga back view',
    image_type: 'gallery',
    sort_order: 3
  },
  {
    image_url: '/uploads/products/lehenga-dupatta-detail.jpg',
    alt_text: 'Dupatta detail view',
    image_type: 'detail',
    sort_order: 4
  }
];

async function addGalleryImages() {
  try {
    // Connect to MongoDB
    await mongoose.connect(MONGODB_URI);
    console.log('Connected to MongoDB');

    // Find categories first
    const kurtiCategory = await Category.findOne({ name: { $regex: /kurti/i } });
    const lehengaCategory = await Category.findOne({ name: { $regex: /lehenga/i } });

    console.log('Found categories:', {
      kurti: kurtiCategory?.name,
      lehenga: lehengaCategory?.name
    });

    // Find some products to add gallery images to
    const kurtiProducts = kurtiCategory ?
      await Product.find({ category: kurtiCategory._id }).limit(5) :
      await Product.find({ name: { $regex: /kurti/i } }).limit(5);

    const lehengaProducts = lehengaCategory ?
      await Product.find({ category: lehengaCategory._id }).limit(5) :
      await Product.find({ name: { $regex: /lehenga/i } }).limit(5);

    console.log(`Found ${kurtiProducts.length} kurti products and ${lehengaProducts.length} lehenga products`);

    // Add gallery images to kurti products
    for (const product of kurtiProducts) {
      await Product.findByIdAndUpdate(
        product._id,
        {
          $set: {
            gallery_images: sampleGalleryImages.map(img => ({
              ...img,
              image_url: img.image_url.replace('kurti', `kurti-${product._id.toString().slice(-4)}`),
              alt_text: `${product.name} - ${img.alt_text}`
            }))
          }
        }
      );
      console.log(`Added gallery images to kurti: ${product.name}`);
    }

    // Add gallery images to lehenga products
    for (const product of lehengaProducts) {
      await Product.findByIdAndUpdate(
        product._id,
        {
          $set: {
            gallery_images: sampleLehengaGallery.map(img => ({
              ...img,
              image_url: img.image_url.replace('lehenga', `lehenga-${product._id.toString().slice(-4)}`),
              alt_text: `${product.name} - ${img.alt_text}`
            }))
          }
        }
      );
      console.log(`Added gallery images to lehenga: ${product.name}`);
    }

    console.log('Gallery images added successfully!');

    // Verify the changes
    const updatedProduct = kurtiCategory ?
      await Product.findOne({ category: kurtiCategory._id }).select('name product_images gallery_images') :
      await Product.findOne({ name: { $regex: /kurti/i } }).select('name product_images gallery_images');

    if (updatedProduct) {
      console.log('\nSample updated product:');
      console.log('Name:', updatedProduct.name);
      console.log('Product images count:', updatedProduct.product_images?.length || 0);
      console.log('Gallery images count:', updatedProduct.gallery_images?.length || 0);
    }

  } catch (error) {
    console.error('Error adding gallery images:', error);
  } finally {
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
  }
}

// Run the script
addGalleryImages();
