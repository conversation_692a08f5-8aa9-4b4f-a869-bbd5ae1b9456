import { useEffect } from 'react';
import { useAppDispatch, useAppSelector } from '@/hooks/hooks';
import {
  fetchMainCategories,
  fetchFeaturedCategories,
  selectMainCategories,
  selectFeaturedCategories,
  selectCategoryLoading,
  selectCategoryError,
} from '@/store/slices/categories/categorySlice';
import {
  fetchActiveMenuConfig,
  selectNavigationCategories,
  selectMegaMenuCategories,
  selectMobileMenuItems,
  selectMenuConfigLoading,
  selectMenuConfigError,
} from '@/store/slices/menuConfigSlice';

// Utility function to safely extract string values from objects
const safeStringValue = (value: any, fallback: string = ''): string => {
  if (typeof value === 'string') return value;
  if (typeof value === 'object' && value !== null) {
    // If it's an object, try to extract name, slug, or _id
    return value.name || value.slug || value._id || String(value);
  }
  return String(value || fallback);
};

// Category interface for navigation
export interface NavigationCategory {
  id: string;
  name: string;
  slug: string;
  path: string;
  subcategories?: NavigationSubcategory[];
  featured?: boolean;
}

export interface NavigationSubcategory {
  id: string;
  name: string;
  slug: string;
  path: string;
  parentCategory: string;
}

// Convert backend category to navigation format
const convertToNavigationCategory = (category: any): NavigationCategory => {
  // Handle both _id (MongoDB) and id formats, with safe string extraction
  const categoryId = safeStringValue(category.id || category._id);
  const categoryName = safeStringValue(category.name);
  const categorySlug = safeStringValue(category.slug);

  return {
    id: categoryId,
    name: categoryName,
    slug: categorySlug,
    path: `/category/${categorySlug}`,
    subcategories: category.subcategories?.map((sub: any) => {
      const subId = safeStringValue(sub.id || sub._id);
      const subName = safeStringValue(sub.name);
      const subSlug = safeStringValue(sub.slug);

      return {
        id: subId,
        name: subName,
        slug: subSlug,
        path: `/category/${categorySlug}/${subSlug}`,
        parentCategory: categorySlug,
      };
    }) || [],
    featured: category.featured || category.is_featured || false,
  };
};

// Static fallback categories matching backend structure
const fallbackCategories: NavigationCategory[] = [
  {
    id: 'sarees',
    name: 'Sarees',
    slug: 'sarees',
    path: '/category/sarees',
    subcategories: [
      { id: 'silk-sarees', name: 'Silk Sarees', slug: 'silk-sarees', path: '/category/silk-sarees', parentCategory: 'sarees' },
      { id: 'cotton-sarees', name: 'Cotton Sarees', slug: 'cotton-sarees', path: '/category/cotton-sarees', parentCategory: 'sarees' },
      { id: 'designer-sarees', name: 'Designer Sarees', slug: 'designer-sarees', path: '/category/designer-sarees', parentCategory: 'sarees' },
    ],
  },
  {
    id: 'lehengas',
    name: 'Lehengas',
    slug: 'lehengas',
    path: '/category/lehengas',
    subcategories: [
      { id: 'bridal-lehengas', name: 'Bridal Lehengas', slug: 'bridal-lehengas', path: '/category/bridal-lehengas', parentCategory: 'lehengas' },
      { id: 'party-wear-lehengas', name: 'Party Wear Lehengas', slug: 'party-wear-lehengas', path: '/category/party-wear-lehengas', parentCategory: 'lehengas' },
      { id: 'festive-lehengas', name: 'Festive Lehengas', slug: 'festive-lehengas', path: '/category/festive-lehengas', parentCategory: 'lehengas' },
    ],
  },
  {
    id: 'kurtis',
    name: 'Kurtis',
    slug: 'kurtis',
    path: '/category/kurtis',
    subcategories: [
      { id: 'anarkali-kurtis', name: 'Anarkali Kurtis', slug: 'anarkali-kurtis', path: '/category/anarkali-kurtis', parentCategory: 'kurtis' },
      { id: 'casual-kurtis', name: 'Casual Kurtis', slug: 'casual-kurtis', path: '/category/casual-kurtis', parentCategory: 'kurtis' },
      { id: 'party-wear-kurtis', name: 'Party Wear Kurtis', slug: 'party-wear-kurtis', path: '/category/party-wear-kurtis', parentCategory: 'kurtis' },
    ],
  },
  {
    id: 'fabrics',
    name: 'Fabrics',
    slug: 'fabrics',
    path: '/category/fabrics',
    subcategories: [],
  },
  {
    id: 'indo-western',
    name: 'Indo Western',
    slug: 'indo-western',
    path: '/category/indo-western',
    subcategories: [],
  },
  {
    id: 'salwar-kameez',
    name: 'Salwar Kameez',
    slug: 'salwar-kameez',
    path: '/category/salwar-kameez',
    subcategories: [],
  },
  {
    id: 'ethnic-wear',
    name: 'Ethnic Wear',
    slug: 'ethnic-wear',
    path: '/category/ethnic-wear',
    subcategories: [],
  },
  {
    id: 'dupattas',
    name: 'Dupattas',
    slug: 'dupattas',
    path: '/category/dupattas',
    subcategories: [],
  },
  {
    id: 'blouses',
    name: 'Blouses',
    slug: 'blouses',
    path: '/category/blouses',
    subcategories: [],
  },
  {
    id: 'plazos',
    name: 'Plazos',
    slug: 'plazos',
    path: '/category/plazos',
    subcategories: [],
  },
  {
    id: 'new-arrivals',
    name: 'New Arrivals',
    slug: 'new-arrivals',
    path: '/category/new-arrivals',
    subcategories: [],
  },
  {
    id: 'best-sellers',
    name: 'Best Sellers',
    slug: 'best-sellers',
    path: '/category/best-sellers',
    subcategories: [],
  },
  {
    id: 'sales',
    name: 'Sales',
    slug: 'sales',
    path: '/category/sales',
    subcategories: [],
  },
];

// Hook for navigation categories (now uses dynamic menu config with fallback)
export const useNavigationCategories = () => {
  const dispatch = useAppDispatch();

  // Try to get from menu config first
  const menuCategories = useAppSelector(selectNavigationCategories);
  const megaMenuCategories = useAppSelector(selectMegaMenuCategories);
  const menuLoading = useAppSelector(selectMenuConfigLoading);
  const menuError = useAppSelector(selectMenuConfigError);

  // Fallback to old category system
  const backendCategories = useAppSelector(selectMainCategories);
  const featuredCategories = useAppSelector(selectFeaturedCategories);
  const categoryLoading = useAppSelector(selectCategoryLoading);
  const categoryError = useAppSelector(selectCategoryError);

  // Fetch menu config and categories on mount
  useEffect(() => {
    dispatch(fetchActiveMenuConfig('both'));
    dispatch(fetchMainCategories());
    dispatch(fetchFeaturedCategories(6));
  }, [dispatch]);

  // Determine if we have any server data
  const hasMenuData = menuCategories.length > 0;
  const hasBackendData = backendCategories.length > 0;
  const hasAnyServerData = hasMenuData || hasBackendData;

  // Check if both menu config and categories failed to load (server issues)
  const serverUnavailable = (menuError && categoryError) ||
                           (!menuLoading && !categoryLoading && !hasAnyServerData);

  // Use priority: menu config > backend categories > fallback categories
  const navigationCategories: NavigationCategory[] = hasMenuData
    ? menuCategories
    : hasBackendData
    ? backendCategories.map(convertToNavigationCategory)
    : fallbackCategories;

  const navigationFeaturedCategories: NavigationCategory[] = hasMenuData
    ? megaMenuCategories
    : hasBackendData
    ? featuredCategories.length > 0
      ? featuredCategories.map(convertToNavigationCategory)
      : fallbackCategories.slice(0, 3)
    : fallbackCategories.slice(0, 3);

  return {
    categories: navigationCategories,
    featuredCategories: navigationFeaturedCategories,
    loading: menuLoading || categoryLoading,
    error: menuError || categoryError,
    hasBackendData: hasAnyServerData,
    hasMenuConfig: hasMenuData,
    serverUnavailable,
    usingFallback: !hasAnyServerData,
  };
};

// Hook for footer categories (shop section)
export const useFooterCategories = () => {
  const { categories, loading } = useNavigationCategories();

  const footerShopLinks = [
    { name: 'All Collections', path: '/products' },
    { name: 'New Arrivals', path: '/category/collections/new-arrivals' },
    { name: 'Trending Now', path: '/category/collections/trending' },
    { name: 'Sale Items', path: '/category/collections/sale' },
    ...categories.slice(0, 2).map(cat => ({
      name: cat.name,
      path: cat.path,
    })),
  ];

  return {
    shopLinks: footerShopLinks,
    loading,
  };
};

// Hook for mobile menu categories (now uses dynamic menu config with fallback)
export const useMobileMenuCategories = () => {
  const dispatch = useAppDispatch();

  // Try to get from menu config first
  const menuItems = useAppSelector(selectMobileMenuItems);
  const menuLoading = useAppSelector(selectMenuConfigLoading);
  const menuError = useAppSelector(selectMenuConfigError);

  // Fallback to old system
  const { categories, loading: categoryLoading, hasMenuConfig, serverUnavailable, usingFallback } = useNavigationCategories();

  // Fetch menu config on mount
  useEffect(() => {
    dispatch(fetchActiveMenuConfig('mobile-menu'));
  }, [dispatch]);

  // Use menu config if available, otherwise fallback to constructed menu from categories
  const mobileMenuItems = hasMenuConfig && !serverUnavailable ? menuItems : (() => {
    // Find specific categories to ensure proper ordering
    const clothingCategory = categories.find(cat => cat.slug === 'clothing');
    const fabricsCategory = categories.find(cat => cat.slug === 'fabrics');
    const collectionsCategory = categories.find(cat => cat.slug === 'collections');

    return [
      // 1. Shop All
      { name: "Shop All", link: "/products" },

      // 2. Sale
      { name: "Sale", link: "/category/collections/sale" },

      // 3. New Arrivals
      { name: "New Arrivals", link: "/category/collections/new-arrivals" },

      // 4. Categories: Clothing, Fabrics, Collections
      ...(clothingCategory ? [{
        name: clothingCategory.name,
        link: clothingCategory.path,
        subItems: clothingCategory.subcategories?.map(sub => ({
          name: sub.name,
          link: sub.path,
        })) || [],
      }] : []),

      ...(fabricsCategory ? [{
        name: fabricsCategory.name,
        link: fabricsCategory.path,
        subItems: fabricsCategory.subcategories?.map(sub => ({
          name: sub.name,
          link: sub.path,
        })) || [],
      }] : []),

      ...(collectionsCategory ? [{
        name: collectionsCategory.name,
        link: collectionsCategory.path,
        subItems: collectionsCategory.subcategories?.map(sub => ({
          name: sub.name,
          link: sub.path,
        })) || [],
      }] : []),

      // 5. Info & Help
      {
        name: "Info & Help",
        link: "/info/about-us",
        subItems: [
          { name: "About Us", link: "/info/about-us" },
          { name: "Our Stores", link: "/info/our-stores" },
          { name: "Contact Us", link: "/info/contact" },
          { name: "FAQ", link: "/customer-service/faq" },
          { name: "Returns Policy", link: "/customer-service/returns" },
          { name: "Terms & Conditions", link: "/info/terms" },
          { name: "Privacy Policy", link: "/info/privacy" },
        ],
      },
    ];
  })();

  return {
    menuItems: mobileMenuItems,
    loading: menuLoading || categoryLoading,
    hasMenuConfig,
    serverUnavailable,
    usingFallback,
    error: menuError,
  };
};
