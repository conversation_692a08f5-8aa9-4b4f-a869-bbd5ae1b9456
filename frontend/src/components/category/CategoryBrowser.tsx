import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Grid, List, Filter, Search, ChevronDown } from 'lucide-react';
import { Link } from 'react-router-dom';
import { categoryService } from '@/services/categoryService';
import { productService } from '@/services/productService';
import ProductDetailCard from '@/components/product/ProductDetailCard';
import { Button } from '@/components/ui/button';

interface Category {
  _id: string;
  name: string;
  slug: string;
  description?: string;
  image?: string;
  banner_image?: string;
  icon?: string;
  is_featured: boolean;
  subcategories?: Category[];
}

interface Product {
  _id: string;
  name: string;
  price: {
    current: number;
    original: number;
  };
  product_images: Array<{
    image_url: string;
    alt_text: string;
  }>;
  category: string;
  rating: {
    average_rating: number;
    reviews_count: number;
  };
  is_new_arrival?: boolean;
}

interface CategoryBrowserProps {
  categorySlug?: string;
}

const CategoryBrowser: React.FC<CategoryBrowserProps> = ({ categorySlug }) => {
  const [categories, setCategories] = useState<Category[]>([]);
  const [products, setProducts] = useState<Product[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<Category | null>(null);
  const [loading, setLoading] = useState(true);
  const [productsLoading, setProductsLoading] = useState(false);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [sortBy, setSortBy] = useState('popularity');
  const [searchQuery, setSearchQuery] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  // Fetch categories
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        setLoading(true);
        const response = await categoryService.getCategories();
        
        if (response.success) {
          setCategories(response.data.categories);
          
          // If categorySlug is provided, find and select that category
          if (categorySlug) {
            const category = response.data.categories.find(
              (cat: Category) => cat.slug === categorySlug
            );
            if (category) {
              setSelectedCategory(category);
            }
          }
        }
      } catch (error) {
        console.error('Error fetching categories:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchCategories();
  }, [categorySlug]);

  // Fetch products when category changes
  useEffect(() => {
    const fetchProducts = async () => {
      if (!selectedCategory) return;

      try {
        setProductsLoading(true);

        console.log('CategoryBrowser: Fetching products for category:', selectedCategory.name);

        // Use category name for filtering - backend expects exact category name match
        const response = await productService.getAllProducts({
          category: selectedCategory.name, // This should match the exact category name from backend
          page: currentPage,
          limit: 12,
          sortBy: sortBy as any,
          search: searchQuery || undefined
        });

        console.log('CategoryBrowser: API response:', response);

        if (response.success && response.data) {
          const products = response.data.products || [];
          setProducts(products);
          setTotalPages(Math.ceil((response.data.pagination?.totalProducts || response.data.total || products.length) / 12));
          console.log('CategoryBrowser: Found products:', products.length);
        } else {
          console.warn('CategoryBrowser: No products found for category:', selectedCategory.name, response);
          setProducts([]);
          setTotalPages(1);
        }
      } catch (error) {
        console.error('CategoryBrowser: Error fetching products for category:', selectedCategory.name, error);
        setProducts([]);
        setTotalPages(1);
      } finally {
        setProductsLoading(false);
      }
    };

    fetchProducts();
  }, [selectedCategory, currentPage, sortBy, searchQuery]);

  const handleCategorySelect = (category: Category) => {
    setSelectedCategory(category);
    setCurrentPage(1);
    setSearchQuery('');
  };

  const handleSearch = (query: string) => {
    setSearchQuery(query);
    setCurrentPage(1);
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/3 mb-8"></div>
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4 mb-8">
            {[...Array(6)].map((_, i) => (
              <div key={i} className="h-32 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Page Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-light text-gray-900 mb-4">
          {selectedCategory ? selectedCategory.name : 'Browse Categories'}
        </h1>
        {selectedCategory?.description && (
          <p className="text-gray-600 max-w-2xl">{selectedCategory.description}</p>
        )}
      </div>

      {/* Category Navigation */}
      <div className="mb-8">
        <h2 className="text-lg font-medium text-gray-900 mb-4">Categories</h2>
        <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
          {categories.map((category) => (
            <motion.button
              key={category._id}
              onClick={() => handleCategorySelect(category)}
              className={`p-4 rounded-lg border transition-all duration-300 ${
                selectedCategory?._id === category._id
                  ? 'border-gray-900 bg-gray-50'
                  : 'border-gray-200 hover:border-gray-300'
              }`}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              {category.icon && (
                <img
                  src={category.icon}
                  alt={category.name}
                  className="w-12 h-12 object-cover rounded mb-2 mx-auto"
                />
              )}
              <h3 className="font-medium text-sm text-center">{category.name}</h3>
              {category.subcategories && category.subcategories.length > 0 && (
                <p className="text-xs text-gray-500 text-center mt-1">
                  {category.subcategories.length} subcategories
                </p>
              )}
            </motion.button>
          ))}
        </div>
      </div>

      {/* Subcategories */}
      {selectedCategory?.subcategories && selectedCategory.subcategories.length > 0 && (
        <div className="mb-8">
          <h3 className="text-lg font-medium text-gray-900 mb-4">
            {selectedCategory.name} Subcategories
          </h3>
          <div className="flex flex-wrap gap-2">
            {selectedCategory.subcategories.map((subcategory) => (
              <Link
                key={subcategory._id}
                to={`/category/${subcategory.slug}`}
                className="px-4 py-2 bg-gray-100 hover:bg-gray-200 rounded-full text-sm font-medium transition-colors"
              >
                {subcategory.name}
              </Link>
            ))}
          </div>
        </div>
      )}

      {/* Products Section */}
      {selectedCategory && (
        <div>
          {/* Search and Filters */}
          <div className="flex flex-col md:flex-row gap-4 mb-6">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <input
                  type="text"
                  placeholder={`Search in ${selectedCategory.name}...`}
                  value={searchQuery}
                  onChange={(e) => handleSearch(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-gray-900 focus:border-transparent"
                />
              </div>
            </div>
            
            <div className="flex items-center gap-4">
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
                className="border border-gray-300 rounded px-3 py-2 text-sm"
              >
                <option value="popularity">Most Popular</option>
                <option value="price_low_to_high">Price: Low to High</option>
                <option value="price_high_to_low">Price: High to Low</option>
                <option value="newest">Newest First</option>
                <option value="rating">Highest Rated</option>
              </select>
              
              <div className="flex border border-gray-300 rounded">
                <button
                  onClick={() => setViewMode('grid')}
                  className={`p-2 ${viewMode === 'grid' ? 'bg-gray-900 text-white' : 'text-gray-600'}`}
                >
                  <Grid className="h-4 w-4" />
                </button>
                <button
                  onClick={() => setViewMode('list')}
                  className={`p-2 ${viewMode === 'list' ? 'bg-gray-900 text-white' : 'text-gray-600'}`}
                >
                  <List className="h-4 w-4" />
                </button>
              </div>
            </div>
          </div>

          {/* Products Grid */}
          {productsLoading ? (
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
              {[...Array(8)].map((_, i) => (
                <div key={i} className="animate-pulse">
                  <div className="aspect-[4/5] bg-gray-200 rounded-lg mb-4"></div>
                  <div className="h-4 bg-gray-200 rounded mb-2"></div>
                  <div className="h-4 bg-gray-200 rounded w-2/3"></div>
                </div>
              ))}
            </div>
          ) : products.length === 0 ? (
            <div className="text-center py-12">
              <p className="text-gray-600">No products found in this category.</p>
            </div>
          ) : (
            <div className={`grid gap-6 ${
              viewMode === 'grid' 
                ? 'grid-cols-2 md:grid-cols-3 lg:grid-cols-4' 
                : 'grid-cols-1'
            }`}>
              {products.map((product) => (
                <ProductDetailCard
                  key={product._id}
                  image={product.product_images[0]?.image_url}
                  name={product.name}
                  price={product.price.current}
                  discountPrice={product.price.original > product.price.current ? product.price.original : undefined}
                  link={`/product/${product._id}`}
                  isNew={product.is_new_arrival}
                  category={selectedCategory.name}
                  rating={product.rating.average_rating}
                  reviewCount={product.rating.reviews_count}
                  productId={product._id}
                  onAddToCart={() => {}}
                  onAddToWishlist={() => {}}
                />
              ))}
            </div>
          )}

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="flex justify-center mt-8">
              <div className="flex items-center gap-2">
                {[...Array(totalPages)].map((_, index) => (
                  <button
                    key={index}
                    onClick={() => setCurrentPage(index + 1)}
                    className={`px-3 py-1 rounded ${
                      currentPage === index + 1
                        ? 'bg-gray-900 text-white'
                        : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                    }`}
                  >
                    {index + 1}
                  </button>
                ))}
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default CategoryBrowser;
