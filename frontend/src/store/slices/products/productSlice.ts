import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import type { RootState } from '@/store';
import { productService, type ProductFilters } from '@/services/productService';

// Complete Product interfaces matching backend API
export interface ProductPrice {
  original: number;
  current: number;
  currency: string;
  discount_percentage: number;
  savings: number;
  _id?: string;
}

export interface ProductColor {
  color_name: string;
  color_hex: string;
  image_url: string;
  is_available: boolean;
  _id?: string;
}

export interface ProductSize {
  size_name: string;
  available_stock: number;
  is_available: boolean;
  _id?: string;
}

export interface ProductImage {
  image_url: string;
  alt_text: string;
  is_primary: boolean;
  sort_order?: number;
  _id?: string;
}

export interface ProductRating {
  average_rating: number;
  reviews_count: number;
  rating_breakdown?: {
    "5_star": number;
    "4_star": number;
    "3_star": number;
    "2_star": number;
    "1_star": number;
    _id?: string;
  };
  reviews?: any[];
  _id?: string;
}

export interface ProductInventory {
  total_stock: number;
  reserved_stock: number;
  available_stock: number;
  low_stock_threshold: number;
  track_inventory: boolean;
  _id?: string;
}

export interface ProductDeliveryInfo {
  free_shipping: boolean;
  delivery_time: string;
  delivery_cost: number;
  express_delivery?: {
    available: boolean;
    cost: number;
    delivery_time: string;
  };
  _id?: string;
}

export interface ProductSocialProof {
  likes: number;
  shares: number;
  comments: number;
  wishlist_count: number;
  view_count: number;
  _id?: string;
}

export interface Product {
  _id: string;
  product_id: string;
  name: string;
  slug: string;
  brand: string;
  category: string | { _id: string; name: string; slug: string };
  subcategory: string;
  description: string;
  short_description?: string;
  price: ProductPrice;
  colors: ProductColor[];
  sizes: ProductSize[];
  material: string;
  style: string;
  occasion: string;
  season: string;
  care_instructions?: string;
  tags: string[];
  rating: ProductRating;
  availability: string;
  stock_status: string;
  inventory?: ProductInventory;
  delivery_info?: ProductDeliveryInfo;
  product_images: ProductImage[];
  gallery_images?: ProductImage[];
  related_products?: any[];
  social_proof?: ProductSocialProof;
  seo?: {
    meta_title: string;
    meta_description: string;
    keywords: string[];
    _id?: string;
  };
  product_url: string;
  is_featured: boolean;
  is_bestseller: boolean;
  is_new_arrival: boolean;
  is_trending?: boolean;
  is_active?: boolean;
  dimensions?: {
    unit: string;
  };
  content_management?: any;
  createdAt: string;
  updatedAt: string;
  __v?: number;
}

// ProductFilters is imported above and re-exported for external use
export type { ProductFilters };

// Simplified Product State
export interface ProductState {
  // All products (mixed)
  allProducts: Product[];
  allProductsLoading: boolean;
  allProductsError: string | null;

  // Current product (for product detail page)
  currentProduct: Product | null;
  currentProductLoading: boolean;
  currentProductError: string | null;

  // Recommended products
  recommendedProducts: Product[];
  recommendedLoading: boolean;
  recommendedError: string | null;

  // Featured products
  featuredProducts: Product[];
  featuredLoading: boolean;
  featuredError: string | null;

  // New arrivals
  newArrivals: Product[];
  newArrivalsLoading: boolean;
  newArrivalsError: string | null;

  // Bestsellers
  bestsellers: Product[];
  bestsellersLoading: boolean;
  bestsellersError: string | null;

  // Kurti products
  kurtiProducts: Product[];
  kurtiLoading: boolean;
  kurtiError: string | null;

  // Lehenga products
  lehengaProducts: Product[];
  lehengaLoading: boolean;
  lehengaError: string | null;

  // Status tracking
  status: {
    all: 'idle' | 'loading' | 'succeeded' | 'failed';
    currentProduct: 'idle' | 'loading' | 'succeeded' | 'failed';
    recommended: 'idle' | 'loading' | 'succeeded' | 'failed';
    featured: 'idle' | 'loading' | 'succeeded' | 'failed';
    newArrivals: 'idle' | 'loading' | 'succeeded' | 'failed';
    bestsellers: 'idle' | 'loading' | 'succeeded' | 'failed';
    kurti: 'idle' | 'loading' | 'succeeded' | 'failed';
    lehenga: 'idle' | 'loading' | 'succeeded' | 'failed';
  };
}

const initialState: ProductState = {
  allProducts: [],
  allProductsLoading: false,
  allProductsError: null,

  currentProduct: null,
  currentProductLoading: false,
  currentProductError: null,

  recommendedProducts: [],
  recommendedLoading: false,
  recommendedError: null,

  featuredProducts: [],
  featuredLoading: false,
  featuredError: null,

  newArrivals: [],
  newArrivalsLoading: false,
  newArrivalsError: null,

  bestsellers: [],
  bestsellersLoading: false,
  bestsellersError: null,

  kurtiProducts: [],
  kurtiLoading: false,
  kurtiError: null,

  lehengaProducts: [],
  lehengaLoading: false,
  lehengaError: null,

  status: {
    all: 'idle',
    currentProduct: 'idle',
    recommended: 'idle',
    featured: 'idle',
    newArrivals: 'idle',
    bestsellers: 'idle',
    kurti: 'idle',
    lehenga: 'idle',
  },
};

// Dynamic product fetching thunk
export const fetchProducts = createAsyncThunk(
  'products/fetchProducts',
  async (filters: ProductFilters = {}) => {
    const result = await productService.getAllProducts(filters);

    // Handle direct response from productService
    if (result && result.products) {
      return result.products;
    }

    // If result is an array directly
    if (Array.isArray(result)) {
      return result;
    }

    return [];
  }
);

// Specific thunks for different product types
export const fetchFeaturedProducts = createAsyncThunk(
  'products/fetchFeaturedProducts',
  async (limit: number = 8) => {
    const result = await productService.getFeaturedProducts(limit);

    // Handle direct response from productService
    if (result && result.products) {
      return result.products;
    }

    // If result is an array directly
    if (Array.isArray(result)) {
      return result;
    }

    return [];
  }
);

export const fetchNewArrivalProducts = createAsyncThunk(
  'products/fetchNewArrivalProducts',
  async (limit: number = 8) => {
    const result = await productService.getNewArrivalProducts(limit);

    // Handle direct response from productService
    if (result && result.products) {
      return result.products;
    }

    // If result is an array directly
    if (Array.isArray(result)) {
      return result;
    }

    return [];
  }
);

export const fetchBestsellerProducts = createAsyncThunk(
  'products/fetchBestsellerProducts',
  async (limit: number = 8) => {
    const result = await productService.getBestsellerProducts(limit);

    // Handle direct response from productService
    if (result && result.products) {
      return result.products;
    }

    // If result is an array directly
    if (Array.isArray(result)) {
      return result;
    }

    return [];
  }
);

export const fetchKurtiProducts = createAsyncThunk(
  'products/fetchKurtiProducts',
  async (limit: number = 8) => {
    const result = await productService.getAllProducts({ category: 'Kurtis', limit });

    // Handle direct response from productService
    if (result && result.products) {
      return result.products;
    }

    // If result is an array directly
    if (Array.isArray(result)) {
      return result;
    }

    return [];
  }
);

export const fetchLehengaProducts = createAsyncThunk(
  'products/fetchLehengaProducts',
  async (limit: number = 8) => {
    const result = await productService.getAllProducts({ category: 'Lehengas', limit });

    // Handle direct response from productService
    if (result && result.products) {
      return result.products;
    }

    // If result is an array directly
    if (Array.isArray(result)) {
      return result;
    }

    return [];
  }
);

// Fetch individual product by ID
export const fetchProductById = createAsyncThunk(
  'products/fetchProductById',
  async (productId: string) => {
    const result = await productService.getProductById(productId);

    // Handle direct response from productService
    if (result && result.product) {
      return result.product;
    }

    // If result is the product directly
    if (result && result._id) {
      return result;
    }

    throw new Error('Product not found');
  }
);

// Fetch individual product by slug
export const fetchProductBySlug = createAsyncThunk(
  'products/fetchProductBySlug',
  async (slug: string) => {
    const result = await productService.getProductBySlug(slug);

    // Handle direct response from productService
    if (result && result.product) {
      return result.product;
    }

    // If result is the product directly
    if (result && result._id) {
      return result;
    }

    throw new Error('Product not found');
  }
);

// Fetch products by category name (dynamic)
export const fetchProductsByCategory = createAsyncThunk(
  'products/fetchProductsByCategory',
  async ({ categoryName, limit = 12, page = 1 }: { categoryName: string; limit?: number; page?: number }) => {
    const result = await productService.getAllProducts({
      category: categoryName,
      limit,
      page
    });

    // Handle direct response from productService
    if (result && result.data && result.data.products) {
      return {
        products: result.data.products,
        pagination: result.data.pagination || {
          currentPage: page,
          totalPages: Math.ceil((result.data.total || 0) / limit),
          totalProducts: result.data.total || 0
        }
      };
    }

    // If result is an array directly
    if (Array.isArray(result)) {
      return {
        products: result,
        pagination: {
          currentPage: page,
          totalPages: 1,
          totalProducts: result.length
        }
      };
    }

    return {
      products: [],
      pagination: {
        currentPage: page,
        totalPages: 0,
        totalProducts: 0
      }
    };
  }
);

// Fetch product recommendations
export const fetchRecommendations = createAsyncThunk(
  'products/fetchRecommendations',
  async ({ id, limit = 4 }: { id: string; limit?: number }) => {
    try {
      const result = await productService.getRecommendations(id, limit);

      // Handle direct response from productService
      if (result && result.products) {
        return result.products;
      }

      // If result is an array directly
      if (Array.isArray(result)) {
        return result;
      }

      return [];
    } catch (error) {
      // If recommendations endpoint doesn't exist, fallback to fetching similar products
      const fallbackResult = await productService.getAllProducts({ limit });

      if (fallbackResult && fallbackResult.products) {
        return fallbackResult.products;
      }

      if (Array.isArray(fallbackResult)) {
        return fallbackResult;
      }

      return [];
    }
  }
);

// Slice
const productSlice = createSlice({
  name: 'products',
  initialState,
  reducers: {
    clearAllProductsError: (state) => {
      state.allProductsError = null;
    },
    clearCurrentProductError: (state) => {
      state.currentProductError = null;
    },
    clearRecommendedError: (state) => {
      state.recommendedError = null;
    },
    clearFeaturedError: (state) => {
      state.featuredError = null;
    },
    clearNewArrivalsError: (state) => {
      state.newArrivalsError = null;
    },
    clearBestsellersError: (state) => {
      state.bestsellersError = null;
    },
    clearKurtiError: (state) => {
      state.kurtiError = null;
    },
    clearLehengaError: (state) => {
      state.lehengaError = null;
    },
    resetProducts: (state) => {
      state.allProducts = [];
      state.currentProduct = null;
      state.recommendedProducts = [];
      state.featuredProducts = [];
      state.newArrivals = [];
      state.bestsellers = [];
      state.kurtiProducts = [];
      state.lehengaProducts = [];
      state.status.all = 'idle';
      state.status.currentProduct = 'idle';
      state.status.recommended = 'idle';
      state.status.featured = 'idle';
      state.status.newArrivals = 'idle';
      state.status.bestsellers = 'idle';
      state.status.kurti = 'idle';
      state.status.lehenga = 'idle';
      state.allProductsError = null;
      state.currentProductError = null;
      state.recommendedError = null;
      state.featuredError = null;
      state.newArrivalsError = null;
      state.bestsellersError = null;
      state.kurtiError = null;
      state.lehengaError = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch all products
      .addCase(fetchProducts.pending, (state) => {
        state.allProductsLoading = true;
        state.status.all = 'loading';
        state.allProductsError = null;
      })
      .addCase(fetchProducts.fulfilled, (state, action) => {
        state.allProductsLoading = false;
        state.status.all = 'succeeded';
        state.allProducts = action.payload;
      })
      .addCase(fetchProducts.rejected, (state, action) => {
        state.allProductsLoading = false;
        state.status.all = 'failed';
        state.allProductsError = action.error.message || 'Failed to fetch products';
      })
      // Fetch featured products
      .addCase(fetchFeaturedProducts.pending, (state) => {
        state.featuredLoading = true;
        state.status.featured = 'loading';
        state.featuredError = null;
      })
      .addCase(fetchFeaturedProducts.fulfilled, (state, action) => {
        state.featuredLoading = false;
        state.status.featured = 'succeeded';
        state.featuredProducts = action.payload;
      })
      .addCase(fetchFeaturedProducts.rejected, (state, action) => {
        state.featuredLoading = false;
        state.status.featured = 'failed';
        state.featuredError = action.error.message || 'Failed to fetch featured products';
      })
      // Fetch new arrivals
      .addCase(fetchNewArrivalProducts.pending, (state) => {
        state.newArrivalsLoading = true;
        state.status.newArrivals = 'loading';
        state.newArrivalsError = null;
      })
      .addCase(fetchNewArrivalProducts.fulfilled, (state, action) => {
        state.newArrivalsLoading = false;
        state.status.newArrivals = 'succeeded';
        state.newArrivals = action.payload;
      })
      .addCase(fetchNewArrivalProducts.rejected, (state, action) => {
        state.newArrivalsLoading = false;
        state.status.newArrivals = 'failed';
        state.newArrivalsError = action.error.message || 'Failed to fetch new arrival products';
      })
      // Fetch bestsellers
      .addCase(fetchBestsellerProducts.pending, (state) => {
        state.bestsellersLoading = true;
        state.status.bestsellers = 'loading';
        state.bestsellersError = null;
      })
      .addCase(fetchBestsellerProducts.fulfilled, (state, action) => {
        state.bestsellersLoading = false;
        state.status.bestsellers = 'succeeded';
        state.bestsellers = action.payload;
      })
      .addCase(fetchBestsellerProducts.rejected, (state, action) => {
        state.bestsellersLoading = false;
        state.status.bestsellers = 'failed';
        state.bestsellersError = action.error.message || 'Failed to fetch bestseller products';
      })
      // Fetch Kurti products
      .addCase(fetchKurtiProducts.pending, (state) => {
        state.kurtiLoading = true;
        state.status.kurti = 'loading';
        state.kurtiError = null;
      })
      .addCase(fetchKurtiProducts.fulfilled, (state, action) => {
        state.kurtiLoading = false;
        state.status.kurti = 'succeeded';
        state.kurtiProducts = action.payload;
      })
      .addCase(fetchKurtiProducts.rejected, (state, action) => {
        state.kurtiLoading = false;
        state.status.kurti = 'failed';
        state.kurtiError = action.error.message || 'Failed to fetch Kurti products';
      })
      // Fetch Lehenga products
      .addCase(fetchLehengaProducts.pending, (state) => {
        state.lehengaLoading = true;
        state.status.lehenga = 'loading';
        state.lehengaError = null;
      })
      .addCase(fetchLehengaProducts.fulfilled, (state, action) => {
        state.lehengaLoading = false;
        state.status.lehenga = 'succeeded';
        state.lehengaProducts = action.payload;
      })
      .addCase(fetchLehengaProducts.rejected, (state, action) => {
        state.lehengaLoading = false;
        state.status.lehenga = 'failed';
        state.lehengaError = action.error.message || 'Failed to fetch Lehenga products';
      })
      // Fetch product by ID
      .addCase(fetchProductById.pending, (state) => {
        state.currentProductLoading = true;
        state.status.currentProduct = 'loading';
        state.currentProductError = null;
      })
      .addCase(fetchProductById.fulfilled, (state, action) => {
        state.currentProductLoading = false;
        state.status.currentProduct = 'succeeded';
        state.currentProduct = action.payload;
      })
      .addCase(fetchProductById.rejected, (state, action) => {
        state.currentProductLoading = false;
        state.status.currentProduct = 'failed';
        state.currentProductError = action.error.message || 'Failed to fetch product';
      })
      // Fetch product by slug
      .addCase(fetchProductBySlug.pending, (state) => {
        state.currentProductLoading = true;
        state.status.currentProduct = 'loading';
        state.currentProductError = null;
      })
      .addCase(fetchProductBySlug.fulfilled, (state, action) => {
        state.currentProductLoading = false;
        state.status.currentProduct = 'succeeded';
        state.currentProduct = action.payload;
      })
      .addCase(fetchProductBySlug.rejected, (state, action) => {
        state.currentProductLoading = false;
        state.status.currentProduct = 'failed';
        state.currentProductError = action.error.message || 'Failed to fetch product';
      })
      // Fetch recommendations
      .addCase(fetchRecommendations.pending, (state) => {
        state.recommendedLoading = true;
        state.status.recommended = 'loading';
        state.recommendedError = null;
      })
      .addCase(fetchRecommendations.fulfilled, (state, action) => {
        state.recommendedLoading = false;
        state.status.recommended = 'succeeded';
        state.recommendedProducts = action.payload;
      })
      .addCase(fetchRecommendations.rejected, (state, action) => {
        state.recommendedLoading = false;
        state.status.recommended = 'failed';
        state.recommendedError = action.error.message || 'Failed to fetch recommendations';
      });
  },
});

// Actions
export const {
  clearAllProductsError,
  clearCurrentProductError,
  clearRecommendedError,
  clearFeaturedError,
  clearNewArrivalsError,
  clearBestsellersError,
  clearKurtiError,
  clearLehengaError,
  resetProducts
} = productSlice.actions;

// Selectors
export const selectAllProducts = (state: RootState) => state.products.allProducts;
export const selectAllProductsLoading = (state: RootState) => state.products.allProductsLoading;
export const selectAllProductsError = (state: RootState) => state.products.allProductsError;

export const selectCurrentProduct = (state: RootState) => state.products.currentProduct;
export const selectCurrentProductLoading = (state: RootState) => state.products.currentProductLoading;
export const selectCurrentProductError = (state: RootState) => state.products.currentProductError;

export const selectRecommendedProducts = (state: RootState) => state.products.recommendedProducts;
export const selectRecommendedLoading = (state: RootState) => state.products.recommendedLoading;
export const selectRecommendedError = (state: RootState) => state.products.recommendedError;

export const selectFeaturedProducts = (state: RootState) => state.products.featuredProducts;
export const selectFeaturedLoading = (state: RootState) => state.products.featuredLoading;
export const selectFeaturedError = (state: RootState) => state.products.featuredError;

export const selectNewArrivalProducts = (state: RootState) => state.products.newArrivals;
export const selectNewArrivalLoading = (state: RootState) => state.products.newArrivalsLoading;
export const selectNewArrivalError = (state: RootState) => state.products.newArrivalsError;

export const selectBestsellerProducts = (state: RootState) => state.products.bestsellers;
export const selectBestsellerLoading = (state: RootState) => state.products.bestsellersLoading;
export const selectBestsellerError = (state: RootState) => state.products.bestsellersError;

export const selectKurtiProducts = (state: RootState) => state.products.kurtiProducts;
export const selectKurtiProductsLoading = (state: RootState) => state.products.kurtiLoading;
export const selectKurtiProductsError = (state: RootState) => state.products.kurtiError;

export const selectLehengaProducts = (state: RootState) => state.products.lehengaProducts;
export const selectLehengaProductsLoading = (state: RootState) => state.products.lehengaLoading;
export const selectLehengaProductsError = (state: RootState) => state.products.lehengaError;

// Status selectors
export const selectAllProductsStatus = (state: RootState) => state.products.status.all;
export const selectCurrentProductStatus = (state: RootState) => state.products.status.currentProduct;
export const selectRecommendedStatus = (state: RootState) => state.products.status.recommended;
export const selectFeaturedStatus = (state: RootState) => state.products.status.featured;
export const selectNewArrivalStatus = (state: RootState) => state.products.status.newArrivals;
export const selectBestsellerStatus = (state: RootState) => state.products.status.bestsellers;
export const selectKurtiProductsStatus = (state: RootState) => state.products.status.kurti;
export const selectLehengaProductsStatus = (state: RootState) => state.products.status.lehenga;

// Export reducer
export default productSlice.reducer;
