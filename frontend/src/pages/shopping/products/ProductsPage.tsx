import React, { useEffect, useState, useRef } from 'react';
import { useParams, useSearchParams, Link, useLocation } from 'react-router-dom';
import { useAppSelector, useAppDispatch } from '@/hooks/hooks';
import { useNavigationCategories } from '@/hooks/useCategories';

import {
  fetchProducts,
  selectAllProducts,
  selectAllProductsLoading,
  selectAllProductsError,
  type ProductFilters,
  type Product
} from '@/store/slices/products/productSlice';
import {
  fetchWishlist,
} from '@/store/slices/wishlist/wishlistSlice';
import CardTypeThree from '@/components/cards/CardTypeThree';
import QuickViewModal from '@/components/modals/QuickViewModal';
import { Slider } from '@/components/ui/slider';
import { Button } from '@/components/ui/button';

import {
  Filter,
  SlidersHorizontal,
  ChevronDown,
  ChevronUp,
  Tag,
  Sparkles,
  Percent,
  X,
  Home,
  Grid,
  ChevronRight,
  ChevronLeft,
  ArrowUpDown,
  ShoppingBag,
  Shirt,
  Palette,
  Star,
  Package
} from 'lucide-react';

// Helper functions for product data extraction

// Helper function to get primary product image
const getPrimaryProductImage = (images: Product['product_images']): string => {
  const primaryImage = images.find(img => img.is_primary);
  return primaryImage?.image_url || images[0]?.image_url || '/placeholder-image.jpg';
};

// Helper function to get category name
const getCategoryName = (category: Product['category']): string => {
  if (typeof category === 'string') return category;
  return category?.name || '';
};

// Helper function to convert backend product to UI format for existing components
const convertProductForUI = (backendProduct: Product) => {
  return {
    id: backendProduct.product_id,
    name: backendProduct.name,
    slug: backendProduct.slug,
    description: backendProduct.description,
    price: backendProduct.price.original,
    discountPrice: backendProduct.price.current !== backendProduct.price.original ? backendProduct.price.current : undefined,
    image: getPrimaryProductImage(backendProduct.product_images),
    category: getCategoryName(backendProduct.category),
    subcategory: backendProduct.subcategory,
    tags: backendProduct.tags,
    isNew: backendProduct.is_new_arrival,
    rating: backendProduct.rating.average_rating,
    color: backendProduct.colors[0]?.color_name,
    size: backendProduct.sizes.map(s => s.size_name),
    material: backendProduct.material || '',
    style: backendProduct.style || '',
    occasion: backendProduct.occasion || '',
    season: backendProduct.season || '',
    isOnSale: backendProduct.price.current < backendProduct.price.original,
    isBestSeller: backendProduct.is_bestseller || false,
    isFeatured: backendProduct.is_featured || false,
  };
};

// Function to get category icons
const getCategoryIcon = (categorySlug: string) => {
  const iconMap: { [key: string]: React.ReactElement } = {
    'sarees': <Shirt className="h-5 w-5" />,
    'kurti': <Shirt className="h-5 w-5" />,
    'kurtis': <Shirt className="h-5 w-5" />,
    'lehenga': <Palette className="h-5 w-5" />,
    'lehanga': <Palette className="h-5 w-5" />,
    'lahenga': <Palette className="h-5 w-5" />,
    'salwar': <Shirt className="h-5 w-5" />,
    'palazzo': <Shirt className="h-5 w-5" />,
    'plazo': <Shirt className="h-5 w-5" />,
    'blouse': <Shirt className="h-5 w-5" />,
    'blouce': <Shirt className="h-5 w-5" />,
    'dupatta': <Palette className="h-5 w-5" />,
    'dresses': <Shirt className="h-5 w-5" />,
    'tops': <Shirt className="h-5 w-5" />,
    'bottoms': <Shirt className="h-5 w-5" />,
    'outerwear': <Package className="h-5 w-5" />,
    'accessories': <Star className="h-5 w-5" />,
    'collections': <Sparkles className="h-5 w-5" />,
    'new-arrivals': <Sparkles className="h-5 w-5" />,
    'bestsellers': <Star className="h-5 w-5" />,
    'best-sellers': <Star className="h-5 w-5" />,
  };

  return iconMap[categorySlug.toLowerCase()] || <Package className="h-5 w-5" />;
};

// Mobile Bottom Navigation Component
interface MobileBottomNavProps {
  sortBy: string;
  setSortBy: (value: string) => void;
}

const MobileBottomNav: React.FC<MobileBottomNavProps> = ({ sortBy, setSortBy }) => {
  const location = useLocation();
  const [showCategories, setShowCategories] = useState(false);
  const [showSortOptions, setShowSortOptions] = useState(false);
  const { categories } = useNavigationCategories();

  // Sort options
  const sortOptions = [
    { label: "Newest First", value: "newest" },
    { label: "Price: Low to High", value: "price-asc" },
    { label: "Price: High to Low", value: "price-desc" },
    { label: "Best Rating", value: "rating" },
    { label: "Best Selling", value: "bestselling" },
    { label: "Biggest Discount", value: "discount" }
  ];

  // Check if a path is active
  const isActive = (path: string): boolean => {
    if (path === '/') {
      return location.pathname === '/';
    }
    return location.pathname.includes(path);
  };

  // Close dropdowns when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as HTMLElement;
      if (!target.closest('.categories-dropdown') && !target.closest('.categories-button')) {
        setShowCategories(false);
      }
      if (!target.closest('.sort-dropdown') && !target.closest('.sort-button')) {
        setShowSortOptions(false);
      }
    };

    document.addEventListener('click', handleClickOutside);
    return () => {
      document.removeEventListener('click', handleClickOutside);
    };
  }, []);

  // Close dropdowns when navigating
  useEffect(() => {
    setShowCategories(false);
    setShowSortOptions(false);
  }, [location.pathname]);

  return (
    <>
      {/* Categories dropdown */}
      {showCategories && (
        <div className="md:hidden fixed bottom-16 left-0 right-0 bg-white border-t border-[#f3e8e8] shadow-2xl z-40 categories-dropdown max-h-[70vh] overflow-y-auto pb-safe backdrop-blur-sm">
          {/* Header with gradient background */}
          <div className="sticky top-0 bg-gradient-to-r from-[#6d3d3d] to-[#8b5a5a] px-4 py-3 border-b border-[#f3e8e8]">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center">
                  <Grid className="h-4 w-4 text-white" />
                </div>
                <h3 className="font-semibold text-white text-lg">Shop by Category</h3>
              </div>
              <button
                onClick={() => setShowCategories(false)}
                className="p-2 text-white/80 hover:text-white hover:bg-white/10 rounded-full transition-all duration-200"
              >
                <X className="h-5 w-5" />
              </button>
            </div>
          </div>

          {/* Categories content */}
          <div className="p-4 bg-gradient-to-b from-gray-50 to-white">
            <div className="space-y-4">
              {categories.map((category) => (
                <div key={category.id} className="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
                  {/* Main Category */}
                  <Link
                    to={category.path}
                    onClick={() => setShowCategories(false)}
                    className={`block p-4 transition-all duration-200 ${
                      isActive(category.path)
                        ? 'bg-gradient-to-r from-[#f9e8e8] to-[#fdf2f2] border-l-4 border-[#6d3d3d]'
                        : 'hover:bg-gradient-to-r hover:from-gray-50 hover:to-gray-25 hover:border-l-4 hover:border-[#6d3d3d]/30'
                    }`}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        {/* Category Icon */}
                        <div className={`w-10 h-10 rounded-lg flex items-center justify-center ${
                          isActive(category.path)
                            ? 'bg-[#6d3d3d] text-white'
                            : 'bg-gradient-to-br from-gray-100 to-gray-200 text-[#6d3d3d]'
                        }`}>
                          {getCategoryIcon(category.slug)}
                        </div>
                        <div>
                          <h4 className={`font-semibold text-base ${
                            isActive(category.path) ? 'text-[#6d3d3d]' : 'text-gray-800'
                          }`}>
                            {category.name}
                          </h4>
                          <p className="text-xs text-gray-500 mt-0.5">
                            {category.subcategories?.length || 0} items
                          </p>
                        </div>
                      </div>
                      <ChevronRight className={`h-5 w-5 transition-transform duration-200 ${
                        isActive(category.path) ? 'text-[#6d3d3d] transform rotate-90' : 'text-gray-400'
                      }`} />
                    </div>
                  </Link>

                  {/* Subcategories */}
                  {category.subcategories && category.subcategories.length > 0 && (
                    <div className="border-t border-gray-100 bg-gray-50/50">
                      <div className="grid grid-cols-2 gap-1 p-3">
                        {category.subcategories.slice(0, 6).map((subcategory) => (
                          <Link
                            key={subcategory.id}
                            to={subcategory.path}
                            onClick={() => setShowCategories(false)}
                            className={`group flex items-center space-x-2 p-2.5 rounded-lg text-xs transition-all duration-200 ${
                              isActive(subcategory.path)
                                ? 'bg-[#6d3d3d] text-white shadow-sm'
                                : 'text-gray-600 hover:bg-white hover:text-[#6d3d3d] hover:shadow-sm'
                            }`}
                          >
                            <div className={`w-1.5 h-1.5 rounded-full transition-colors ${
                              isActive(subcategory.path)
                                ? 'bg-white'
                                : 'bg-[#6d3d3d]/30 group-hover:bg-[#6d3d3d]'
                            }`} />
                            <span className="font-medium truncate">{subcategory.name}</span>
                          </Link>
                        ))}
                        {category.subcategories.length > 6 && (
                          <Link
                            to={category.path}
                            onClick={() => setShowCategories(false)}
                            className="flex items-center justify-center p-2.5 rounded-lg text-xs text-[#6d3d3d] hover:bg-white hover:shadow-sm transition-all duration-200 border border-dashed border-[#6d3d3d]/30"
                          >
                            <span className="font-medium">+{category.subcategories.length - 6} more</span>
                          </Link>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>

            {/* Quick Actions */}
            <div className="mt-6 pt-4 border-t border-gray-200">
              <div className="grid grid-cols-2 gap-3">
                <Link
                  to="/category/collections/new-arrivals"
                  onClick={() => setShowCategories(false)}
                  className="flex items-center justify-center space-x-2 p-3 bg-gradient-to-r from-[#6d3d3d] to-[#8b5a5a] text-white rounded-lg hover:shadow-lg transition-all duration-200"
                >
                  <Sparkles className="h-4 w-4" />
                  <span className="font-medium text-sm">New Arrivals</span>
                </Link>
                <Link
                  to="/category/collections/bestsellers"
                  onClick={() => setShowCategories(false)}
                  className="flex items-center justify-center space-x-2 p-3 bg-gradient-to-r from-amber-500 to-orange-500 text-white rounded-lg hover:shadow-lg transition-all duration-200"
                >
                  <span className="text-sm">🔥</span>
                  <span className="font-medium text-sm">Best Sellers</span>
                </Link>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Sort Options dropdown */}
      {showSortOptions && (
        <div className="md:hidden fixed bottom-16 left-0 right-0 bg-white border-t border-[#f3e8e8] shadow-lg z-40 sort-dropdown max-h-[60vh] overflow-y-auto pb-safe">
          <div className="py-4 px-3">
            <div className="flex items-center justify-between mb-2 px-1">
              <h3 className="font-medium text-[#6d3d3d]">Sort Products</h3>
              <button
                onClick={() => setShowSortOptions(false)}
                className="p-1 text-gray-500 hover:text-[#6d3d3d]"
              >
                <X className="h-4 w-4" />
              </button>
            </div>
            <div className="space-y-2 mt-2">
              {sortOptions.map((option) => (
                <button
                  key={option.value}
                  onClick={() => {
                    setSortBy(option.value);
                    setShowSortOptions(false);
                  }}
                  className={`w-full py-3 px-3.5 rounded-md text-sm text-left transition-colors ${
                    sortBy === option.value
                      ? 'bg-[#f9e8e8] text-[#6d3d3d] font-medium border border-[#f3e8e8]'
                      : 'text-gray-700 hover:bg-gray-50 border border-gray-100'
                  }`}
                >
                  {option.label}
                </button>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Bottom Navigation Bar */}
      <div className="md:hidden fixed bottom-0 left-0 right-0 bg-white border-t border-[#f3e8e8] shadow-lg z-50 safe-area-bottom">
        <div className="flex justify-around items-center h-16">
          {/* Home */}
          <Link
            to="/"
            className="flex flex-col items-center justify-center h-full w-full transition-colors duration-200"
          >
            <span className={`${isActive('/') ? 'text-[#6d3d3d]' : 'text-gray-500'}`}>
              <Home className="h-5 w-5" />
            </span>
            <span className={`text-xs mt-1 ${isActive('/') ? 'text-[#6d3d3d] font-medium' : 'text-gray-500'}`}>
              Home
            </span>
          </Link>

          {/* Categories */}
          <button
            onClick={() => setShowCategories(!showCategories)}
            className="flex flex-col items-center justify-center h-full w-full transition-colors duration-200 categories-button"
          >
            <span className={`${(isActive('/category/') && !isActive('/category/new-arrivals')) || showCategories ? 'text-[#6d3d3d]' : 'text-gray-500'}`}>
              <Grid className="h-5 w-5" />
            </span>
            <span className={`text-xs mt-1 ${(isActive('/category/') && !isActive('/category/new-arrivals')) || showCategories ? 'text-[#6d3d3d] font-medium' : 'text-gray-500'}`}>
              Categories
            </span>
          </button>

          {/* New Arrivals */}
          <Link
            to="/category/new-arrivals"
            className="flex flex-col items-center justify-center h-full w-full transition-colors duration-200"
          >
            <span className={`${isActive('/category/new-arrivals') ? 'text-[#6d3d3d]' : 'text-gray-500'}`}>
              <Sparkles className="h-5 w-5" />
            </span>
            <span className={`text-xs mt-1 ${isActive('/category/new-arrivals') ? 'text-[#6d3d3d] font-medium' : 'text-gray-500'}`}>
              New Arrivals
            </span>
          </Link>

          {/* Sort By */}
          <button
            onClick={() => setShowSortOptions(!showSortOptions)}
            className="flex flex-col items-center justify-center h-full w-full transition-colors duration-200 sort-button"
          >
            <span className={`${showSortOptions ? 'text-[#6d3d3d]' : 'text-gray-500'}`}>
              <ArrowUpDown className="h-5 w-5" />
            </span>
            <span className={`text-xs mt-1 ${showSortOptions ? 'text-[#6d3d3d] font-medium' : 'text-gray-500'}`}>
              Sort By
            </span>
          </button>
        </div>
      </div>
    </>
  );
};

// Function to get category descriptions
const getCategoryDescription = (category?: string, subcategory?: string): string => {
  if (subcategory === 'new-arrival' || subcategory === 'new-arrivals' || category === 'new-arrivals') {
    return "Discover our latest arrivals, fresh from the designer's studio. Stay ahead of the fashion curve with our newest styles that blend contemporary trends with timeless elegance.";
  }

  switch (category) {
    case 'dresses':
      return 'Discover our stunning collection of dresses for every occasion. From casual day dresses to elegant evening wear, find the perfect style to express your unique personality.';
    case 'tops':
      return 'Elevate your wardrobe with our versatile collection of tops. From casual tees to elegant blouses, we have styles to suit every mood and occasion.';
    case 'bottoms':
      return 'Complete your look with our stylish bottoms collection. From comfortable jeans to elegant skirts, find the perfect match for any outfit.';
    case 'outerwear':
      return 'Stay stylish in any weather with our premium outerwear collection. From lightweight jackets to cozy coats, we have the perfect layer for every season.';
    case 'accessories':
      return 'Add the perfect finishing touch to your outfit with our curated accessories collection. From statement jewelry to elegant scarves, elevate your style with these essential pieces.';
    default:
      return 'Explore our curated collection of fashion essentials. Each piece is thoughtfully designed to combine style, comfort, and quality for the modern woman.';
  }
};

const ProductsPage: React.FC = () => {
  const { category: rawCategory, subcategory: rawSubcategory } = useParams<{ category?: string; subcategory?: string }>();
  const [searchParams] = useSearchParams();
  const dispatch = useAppDispatch();

  // Utility function to safely extract string from potentially object values
  const safeStringExtract = (value: any): string => {
    if (typeof value === 'string') return value;
    if (typeof value === 'object' && value !== null) {
      return value.slug || value.name || value._id || String(value);
    }
    return String(value || '');
  };

  // Map frontend category/subcategory names to backend category names
  const mapToBackendCategory = (frontendCategory: string, parentCategory?: string): string => {
    // Handle the hierarchical navigation structure
    // URLs like /category/clothing/lehenga should map to backend category "Lehengas"

    const categoryMap: { [key: string]: string } = {
      // Direct category mappings (for URLs like /category/sarees)
      'sarees': 'Sarees',
      'saree': 'Sarees',
      'lehengas': 'Lehengas',
      'lehenga': 'Lehengas',
      'fabrics': 'Fabrics',
      'fabric': 'Fabrics',
      'ethnic-wear': 'Ethnic Wear',
      'ethnic': 'Ethnic Wear',
      'kurtis': 'Kurtis',
      'kurti': 'Kurtis',
      'salwar-kameez': 'Salwar Kameez',
      'salwar': 'Salwar Kameez',
      'dupattas': 'Dupattas',
      'dupatta': 'Dupattas',
      'blouses': 'Blouses',
      'blouse': 'Blouses',
      'plazos': 'Plazos',
      'plazo': 'Plazos',
      'palazzo': 'Plazos',
      'indo-western': 'Indo Western',
      'indowestern': 'Indo Western',
      'chicken-kurti': 'Chicken Kurti',
      'chickenkurti': 'Chicken Kurti',
      'new-arrivals': 'New Arrivals',
      'new-arrival': 'New Arrivals',
      'best-sellers': 'Best Sellers',
      'bestsellers': 'Best Sellers',
      'bestseller': 'Best Sellers',
      'sales': 'Sales',
      'sale': 'Sales',
      'suits': 'Salwar Kameez', // Add suits mapping
      'dresses': 'Ethnic Wear', // Add dresses mapping

      // Subcategories for Lehengas
      'bridal-lehengas': 'Lehengas',
      'party-wear-lehengas': 'Lehengas',
      'festive-lehengas': 'Lehengas',
      'fusion': 'Lehengas',
      'traditional': 'Lehengas',
      'party': 'Lehengas',
      'wedding': 'Lehengas',

      // Subcategories for Sarees
      'silk-sarees': 'Sarees',
      'cotton-sarees': 'Sarees',
      'designer-sarees': 'Sarees',
      'silk': 'Sarees',
      'cotton': 'Sarees',
      'designer': 'Sarees',

      // Subcategories for Kurtis
      'anarkali-kurtis': 'Kurtis',
      'casual-kurtis': 'Kurtis',
      'party-wear-kurtis': 'Kurtis',

      // Collections and special categories
      'wedding-collection': 'Lehengas',
      'festival': 'Ethnic Wear',
      'summer': 'Fabrics',
      'linen': 'Fabrics',
      'chiffon': 'Fabrics',
      'georgette': 'Fabrics',
    };

    const lowerCategory = frontendCategory.toLowerCase();
    const mappedCategory = categoryMap[lowerCategory] || frontendCategory;

    console.log('ProductsPage: Category mapping:', {
      input: frontendCategory,
      parentCategory,
      lowerInput: lowerCategory,
      mapped: mappedCategory,
      isClothingSubcategory: parentCategory === 'clothing'
    });

    return mappedCategory;
  };

  // Ensure category and subcategory are always strings (defensive programming)
  const category = safeStringExtract(rawCategory);
  const subcategory = safeStringExtract(rawSubcategory);

  // Redux state - using the unified product state
  const products = useAppSelector(selectAllProducts);
  const isLoading = useAppSelector(selectAllProductsLoading);
  const error = useAppSelector(selectAllProductsError);

  // Determine which products to use based on current route
  const frontendTargetCategory = subcategory || category;
  const backendTargetCategory = frontendTargetCategory ? mapToBackendCategory(frontendTargetCategory) : '';
  const [priceRange, setPriceRange] = useState<[number, number]>([0, 1000]);
  const [showFilters, setShowFilters] = useState(false);
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [sortBy, setSortBy] = useState('newest');
  const [currentPage, setCurrentPage] = useState(1);
  const productsPerPage = 15; // Number of products to show per page

  // Local filter UI state (replacing Redux UI slice)
  const [isFilterFixed] = useState(true); // Enable sticky filter by default
  const [isFilterScrollable] = useState(true); // Enable scrollable filter by default

  // Filter container ref for scroll handling
  const filterContainerRef = useRef<HTMLDivElement>(null);
  const productsContainerRef = useRef<HTMLDivElement>(null);

  // Filter states
  const [selectedColors, setSelectedColors] = useState<string[]>([]);
  const [selectedSizes, setSelectedSizes] = useState<string[]>([]);
  const [selectedMaterials, setSelectedMaterials] = useState<string[]>([]);
  const [selectedStyles, setSelectedStyles] = useState<string[]>([]);
  const [selectedOccasions, setSelectedOccasions] = useState<string[]>([]);
  const [selectedSeasons, setSelectedSeasons] = useState<string[]>([]);
  const [showOnSale, setShowOnSale] = useState<boolean>(false);
  const [showNewArrivals, setShowNewArrivals] = useState<boolean>(false);
  const [showBestSellers, setShowBestSellers] = useState<boolean>(false);
  const [activeFilterSection, setActiveFilterSection] = useState<string | null>("price");

  // Quick view state
  const [quickViewProduct, setQuickViewProduct] = useState<Product | null>(null);
  const [isQuickViewOpen, setIsQuickViewOpen] = useState(false);

  // Get unique values for each filter from products (using correct backend structure)
  const allColors = Array.from(new Set(products.flatMap(product => product.colors?.map(c => c.color_name) || []).filter(Boolean)));
  const allSizes = Array.from(new Set(products.flatMap(product => product.sizes?.map(s => s.size_name) || []).filter(Boolean)));
  const allMaterials = Array.from(new Set(products.map(product => product.material).filter(Boolean)));
  const allStyles = Array.from(new Set(products.map(product => product.style).filter(Boolean)));
  const allOccasions = Array.from(new Set(products.map(product => product.occasion).filter(Boolean)));
  const allSeasons = Array.from(new Set(products.map(product => product.season).filter(Boolean)));
  const allTags = Array.from(new Set(products.flatMap(product => product.tags || [])));

  // Reset to page 1 when filters change
  useEffect(() => {
    setCurrentPage(1);
  }, [
    priceRange,
    selectedTags,
    selectedColors,
    selectedSizes,
    selectedMaterials,
    selectedStyles,
    selectedOccasions,
    selectedSeasons,
    showOnSale,
    showNewArrivals,
    showBestSellers,
    sortBy,
    category,
    subcategory,
    searchParams
  ]);

  // Add CSS styles for filter positioning
  useEffect(() => {
    // Add custom CSS to handle filter positioning
    const styleElement = document.createElement('style');
    styleElement.textContent = `
      .sticky-filter {
        position: sticky;
        top: 20px;
        z-index: 10;
      }
      .scrollable-filter {
        max-height: calc(100vh - 40px);
        overflow-y: auto;
      }
      @media (max-width: 767px) {
        .sticky-filter {
          position: static;
        }
      }
      /* Prevent filter and products from mixing */
      @media (min-width: 768px) {
        .filter-container {
          width: 18%;
          min-width: 200px;
          max-width: 240px;
        }
        .products-container {
          width: 82%;
          flex: 1;
        }
      }
      /* Product grid styling */
      .products-grid {
        grid-template-columns: repeat(2, minmax(0, 1fr));
        gap: 0.75rem;
      }

      @media (min-width: 640px) {
        .products-grid {
          grid-template-columns: repeat(3, minmax(0, 1fr));
          gap: 1rem;
        }
      }

      @media (min-width: 1024px) {
        .products-grid {
          grid-template-columns: repeat(4, minmax(0, 1fr));
        }
      }

      @media (min-width: 1280px) {
        .products-grid {
          grid-template-columns: repeat(4, minmax(0, 1fr));
        }
      }
    `;
    document.head.appendChild(styleElement);

    return () => {
      document.head.removeChild(styleElement);
    };
  }, []);

  // Handle scroll effect for filter section on large screens
  useEffect(() => {
    if (!isFilterFixed) return;

    const handleScroll = () => {
      if (!filterContainerRef.current) return;

      // Only apply on medium and larger screens
      if (window.innerWidth < 768) return;

      const scrollY = window.scrollY;
      const headerHeight = 100; // Approximate header height

      if (scrollY > headerHeight) {
        // Apply sticky positioning
        filterContainerRef.current.classList.add('sticky-filter');
        if (isFilterScrollable) {
          filterContainerRef.current.classList.add('scrollable-filter');
        }
      } else {
        // Remove sticky positioning
        filterContainerRef.current.classList.remove('sticky-filter');
        filterContainerRef.current.classList.remove('scrollable-filter');
      }
    };

    window.addEventListener('scroll', handleScroll);
    window.addEventListener('resize', handleScroll);

    // Initial call to set correct state
    handleScroll();

    return () => {
      window.removeEventListener('scroll', handleScroll);
      window.removeEventListener('resize', handleScroll);
    };
  }, [isFilterFixed, isFilterScrollable]);

  // Fetch products from backend API using current backend structure
  useEffect(() => {
    const search = searchParams.get('q') || searchParams.get('search');

    // Fetch wishlist to check which products are in wishlist
    dispatch(fetchWishlist());

    console.log('ProductsPage: Fetching products with params:', {
      search,
      category,
      subcategory,
      currentPage,
      sortBy,
      url: window.location.pathname
    });

    // Map frontend sort options to backend sort options
    const backendSortBy = sortBy === 'newest' ? 'createdAt' :
                         sortBy === 'price-asc' ? 'price_low' :
                         sortBy === 'price-desc' ? 'price_high' :
                         sortBy === 'rating' ? 'rating' :
                         'createdAt';

    // Build filters for the dynamic product API
    const filters: ProductFilters = {
      page: currentPage,
      limit: productsPerPage,
      sortBy: backendSortBy
    };

    if (search) {
      // Add search query
      filters.search = search;
      console.log('ProductsPage: Using search mode with query:', search);
    } else if (category) {
      // Handle hierarchical navigation structure
      // For URLs like /category/clothing/lehenga, we want to use "lehenga" as the target
      // For URLs like /category/sarees, we want to use "sarees" as the target
      const targetCategory = subcategory || category;
      const mappedCategory = mapToBackendCategory(targetCategory, category);

      console.log('ProductsPage: Using category mode:', {
        originalCategory: category,
        originalSubcategory: subcategory,
        targetCategory,
        mappedCategory,
        urlPattern: subcategory ? `${category}/${subcategory}` : category,
        finalFilters: { ...filters, category: mappedCategory }
      });

      // Add category filter
      filters.category = mappedCategory;
    } else {
      console.log('ProductsPage: Using all products mode (no category or search)');
    }

    console.log('ProductsPage: Final API call with filters:', filters);

    // Dispatch the unified fetchProducts action
    dispatch(fetchProducts(filters));
  }, [dispatch, category, subcategory, searchParams, currentPage, sortBy]);

  // Filter and sort products (adapted for backend structure)
  const filteredAndSortedProducts = products
    .filter(product => {
      // Price filter (using current price)
      const currentPrice = product.price.current;
      const inPriceRange = currentPrice >= priceRange[0] && currentPrice <= priceRange[1];

      // Tags filter
      const matchesTags = selectedTags.length === 0 ||
        selectedTags.some(tag => product.tags?.includes(tag));

      // Color filter
      const matchesColors = selectedColors.length === 0 ||
        (product.colors && product.colors.some(color => selectedColors.includes(color.color_name)));

      // Size filter
      const matchesSizes = selectedSizes.length === 0 ||
        (product.sizes && product.sizes.some(size => selectedSizes.includes(size.size_name)));

      // Material filter
      const matchesMaterials = selectedMaterials.length === 0 ||
        (product.material && selectedMaterials.some(material =>
          product.material!.toLowerCase().includes(material.toLowerCase())
        ));

      // Style filter
      const matchesStyles = selectedStyles.length === 0 ||
        (product.style && selectedStyles.includes(product.style));

      // Occasion filter
      const matchesOccasions = selectedOccasions.length === 0 ||
        (product.occasion && selectedOccasions.includes(product.occasion));

      // Season filter
      const matchesSeasons = selectedSeasons.length === 0 ||
        (product.season && selectedSeasons.includes(product.season));

      // Special filters (sale, new arrivals, best sellers)
      const matchesOnSale = !showOnSale || (product.price.current < product.price.original);
      const matchesNewArrivals = !showNewArrivals || product.is_new_arrival;
      const matchesBestSellers = !showBestSellers; // Would need backend support

      return inPriceRange && matchesTags && matchesColors && matchesSizes &&
        matchesMaterials && matchesStyles && matchesOccasions && matchesSeasons &&
        matchesOnSale && matchesNewArrivals && matchesBestSellers;
    })
    .sort((a, b) => {
      switch (sortBy) {
        case 'price-asc':
          return a.price.current - b.price.current;
        case 'price-desc':
          return b.price.current - a.price.current;
        case 'rating':
          return b.rating.average_rating - a.rating.average_rating;
        case 'bestselling':
          // Sort by best sellers (would need backend support)
          return 0; // No sorting for now
        case 'discount':
          // Sort by discount percentage
          const aDiscount = a.price.discount_percentage || 0;
          const bDiscount = b.price.discount_percentage || 0;
          return bDiscount - aDiscount;
        case 'newest':
        default:
          // In a real app, you'd sort by date
          // For now, prioritize new items
          return (b.is_new_arrival ? 1 : 0) - (a.is_new_arrival ? 1 : 0);
      }
    });

  const handleTagToggle = (tag: string) => {
    setSelectedTags(prev =>
      prev.includes(tag)
        ? prev.filter(t => t !== tag)
        : [...prev, tag]
    );
  };

  const handlePriceChange = (value: number[]) => {
    setPriceRange([value[0], value[1]]);
  };

  const toggleFilters = () => {
    setShowFilters(!showFilters);
  };

  // Filter handlers
  const handleColorToggle = (color: string) => {
    setSelectedColors(prev =>
      prev.includes(color)
        ? prev.filter(c => c !== color)
        : [...prev, color]
    );
  };

  const handleSizeToggle = (size: string) => {
    setSelectedSizes(prev =>
      prev.includes(size)
        ? prev.filter(s => s !== size)
        : [...prev, size]
    );
  };

  const handleMaterialToggle = (material: string) => {
    setSelectedMaterials(prev =>
      prev.includes(material)
        ? prev.filter(m => m !== material)
        : [...prev, material]
    );
  };

  const handleStyleToggle = (style: string) => {
    setSelectedStyles(prev =>
      prev.includes(style)
        ? prev.filter(s => s !== style)
        : [...prev, style]
    );
  };

  const handleOccasionToggle = (occasion: string) => {
    setSelectedOccasions(prev =>
      prev.includes(occasion)
        ? prev.filter(o => o !== occasion)
        : [...prev, occasion]
    );
  };

  const handleSeasonToggle = (season: string) => {
    setSelectedSeasons(prev =>
      prev.includes(season)
        ? prev.filter(s => s !== season)
        : [...prev, season]
    );
  };

  const toggleFilterSection = (section: string) => {
    setActiveFilterSection(activeFilterSection === section ? null : section);
  };

  const clearAllFilters = () => {
    setPriceRange([0, 1000]);
    setSelectedTags([]);
    setSelectedColors([]);
    setSelectedSizes([]);
    setSelectedMaterials([]);
    setSelectedStyles([]);
    setSelectedOccasions([]);
    setSelectedSeasons([]);
    setShowOnSale(false);
    setShowNewArrivals(false);
    setShowBestSellers(false);
    setCurrentPage(1); // Reset to first page when clearing filters
  };

  // Calculate total pages for pagination
  const totalPages = Math.ceil(filteredAndSortedProducts.length / productsPerPage);

  // Handle page change
  const handlePageChange = (pageNumber: number) => {
    setCurrentPage(pageNumber);
    // Scroll to top of products when changing page
    if (productsContainerRef.current) {
      productsContainerRef.current.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }
  };

  // Count active filters
  const activeFilterCount = [
    selectedTags.length > 0,
    selectedColors.length > 0,
    selectedSizes.length > 0,
    selectedMaterials.length > 0,
    selectedStyles.length > 0,
    selectedOccasions.length > 0,
    selectedSeasons.length > 0,
    showOnSale,
    showNewArrivals,
    showBestSellers,
    !(priceRange[0] === 0 && priceRange[1] === 1000)
  ].filter(Boolean).length;

  // Debug logging
  console.log('ProductsPage Debug:', {
    category,
    subcategory,
    frontendTargetCategory,
    backendTargetCategory,
    productsLength: products.length,
    isLoading,
    error,
  });

  // Add error handling
  if (error) {
    return (
      <div className="container mx-auto px-4 py-6 max-w-screen-2xl">
        <div className="text-center py-12 bg-white border border-gray-200">
          <div className="flex flex-col items-center">
            <div className="text-red-500 mb-4">⚠️</div>
            <h3 className="text-lg font-medium text-red-600 mb-2">Error Loading Products</h3>
            <p className="text-gray-500 max-w-md px-4 mb-4">
              {error || 'Unable to load products. Please check if the backend server is running.'}
            </p>
            <Button
              variant="outline"
              className="border-red-300 text-red-600"
              onClick={() => window.location.reload()}
            >
              Try Again
            </Button>
          </div>
        </div>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-6 max-w-screen-2xl">
        {/* Simple loading header skeleton */}
        <div className="mb-6 animate-pulse">
          <div className="h-4 bg-gray-200 w-1/3 mb-4"></div>
          <div className="h-8 bg-gray-200 w-1/2 mx-auto"></div>
        </div>

        <div className="flex flex-col md:flex-row gap-6">
          {/* Loading filter skeleton */}
          <div className="filter-container hidden md:block">
            <div className="bg-white border border-gray-200">
              <div className="p-4 border-b border-gray-200 animate-pulse">
                <div className="h-6 bg-gray-200 w-1/2"></div>
              </div>
              <div className="p-4 animate-pulse">
                <div className="h-4 bg-gray-200 w-3/4 mb-3"></div>
                <div className="flex gap-2">
                  <div className="h-8 bg-gray-200 w-20"></div>
                  <div className="h-8 bg-gray-200 w-24"></div>
                </div>
              </div>
            </div>
          </div>

          {/* Loading products skeleton */}
          <div className="products-container flex-1">
            {/* Category title and description skeleton */}
            <div className="mb-8 text-center animate-pulse">
              <div className="h-8 bg-gray-200 w-1/2 mx-auto mb-4"></div>
              <div className="h-4 bg-gray-200 w-3/4 mx-auto"></div>
              <div className="h-4 bg-gray-200 w-2/3 mx-auto mt-2"></div>
            </div>

            {/* Breadcrumb and sort controls skeleton */}
            <div className="mb-6 flex flex-col sm:flex-row justify-between items-start sm:items-center gap-3 animate-pulse">
              <div className="h-4 bg-gray-200 w-40"></div>
              <div className="h-6 bg-gray-200 w-32 ml-auto"></div>
            </div>

            <div className="products-grid grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-4 2xl:grid-cols-4 gap-4 md:gap-6">
              {[1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15].map((i) => (
                <div key={i} className="animate-pulse">
                  <div className="bg-gray-200 aspect-[3/4] mb-3"></div>
                  <div className="bg-gray-200 h-5 w-3/4 mb-2"></div>
                  <div className="bg-gray-200 h-4 w-1/2"></div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-6 pb-20 md:pb-6 max-w-screen-2xl">
      {/* Quick View Modal */}
      <QuickViewModal
        product={quickViewProduct}
        isOpen={isQuickViewOpen}
        onClose={() => setIsQuickViewOpen(false)}
      />

      {/* Mobile Bottom Navigation */}
      <MobileBottomNav sortBy={sortBy} setSortBy={setSortBy} />

      {/* Floating filter button for mobile - positioned on right side above bottom nav */}
      <div className="md:hidden fixed bottom-20 right-4 z-40">
        <Button
          onClick={toggleFilters}
          className="h-12 px-4 bg-[#000907] hover:bg-[#1D1616] text-white flex items-center justify-center shadow-lg rounded-full"
        >
          <Filter className="h-5 w-5 mr-2" />
          <span>Filters</span>
          {activeFilterCount > 0 && (
            <span className="ml-1.5 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
              {activeFilterCount}
            </span>
          )}
        </Button>
      </div>

      <div className="flex flex-col md:flex-row gap-6">
        {/* Filters - hidden on mobile unless toggled */}
        <div className={`filter-container ${showFilters ? 'block fixed inset-0 z-50 bg-black/40' : 'hidden'} md:block md:static md:bg-transparent`}>
          <div
            ref={filterContainerRef}
            className={`${showFilters ? 'fixed right-0 top-0 bottom-0 w-[85%] max-w-xs overflow-y-auto' : ''} md:static md:w-full
              bg-white border md:border border-gray-200 overflow-hidden
            `}>
            {/* Filter header with count and close/clear buttons */}
            <div className="sticky top-0 z-10 flex items-center justify-between p-4 border-b border-gray-200 bg-white">
              <h2 className="font-semibold text-[#000907] flex items-center">
                <Filter className="h-4 w-4 mr-2" />
                Filters {activeFilterCount > 0 && (
                  <span className="ml-1.5 bg-[#000907] text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                    {activeFilterCount}
                  </span>
                )}
              </h2>
              <div className="flex items-center gap-2">
                {activeFilterCount > 0 && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={clearAllFilters}
                    className="text-xs text-[#d83f31]"
                  >
                    Clear
                  </Button>
                )}
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={toggleFilters}
                  className="md:hidden text-gray-500 h-8 w-8 p-0 flex items-center justify-center"
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            </div>

            {/* Quick filter categories removed */}

            {/* Price Range Filter */}
            <div className="border-b border-gray-200">
              <button
                onClick={() => toggleFilterSection('price')}
                className="w-full p-4 text-left font-medium text-[#6d3d3d] flex items-center justify-between hover:bg-gray-50"
              >
                <span className="flex items-center">
                  <SlidersHorizontal className="h-4 w-4 mr-2" />
                  Price Range
                  {!(priceRange[0] === 0 && priceRange[1] === 1000) && (
                    <span className="ml-2 text-xs bg-[#6d3d3d] text-white px-1.5 py-0.5 rounded">
                      ${priceRange[0]} - ${priceRange[1]}
                    </span>
                  )}
                </span>
                {activeFilterSection === 'price' ?
                  <ChevronUp className="h-4 w-4" /> :
                  <ChevronDown className="h-4 w-4" />
                }
              </button>

              <div
                className={`px-4 pb-4 pt-2 ${
                  activeFilterSection === 'price' ? 'block' : 'hidden'
                }`}
              >
                <div className="flex justify-between mb-2 text-xs text-gray-500">
                  <span>Min: ${priceRange[0]}</span>
                  <span>Max: ${priceRange[1]}</span>
                </div>
                <Slider
                  defaultValue={[priceRange[0], priceRange[1]]}
                  value={[priceRange[0], priceRange[1]]}
                  max={1000}
                  step={10}
                  onValueChange={handlePriceChange}
                  className="my-4"
                />
                <div className="flex justify-between items-center mt-4">
                  <div className="flex-1">
                    <input
                      type="number"
                      value={priceRange[0]}
                      onChange={(e) => handlePriceChange([parseInt(e.target.value) || 0, priceRange[1]])}
                      className="w-full p-2 text-sm border border-gray-300"
                      min="0"
                      max={priceRange[1]}
                    />
                  </div>
                  <span className="mx-2 text-gray-400">-</span>
                  <div className="flex-1">
                    <input
                      type="number"
                      value={priceRange[1]}
                      onChange={(e) => handlePriceChange([priceRange[0], parseInt(e.target.value) || 0])}
                      className="w-full p-2 text-sm border border-gray-300"
                      min={priceRange[0]}
                      max="1000"
                    />
                  </div>
                </div>
              </div>
            </div>

            {/* Color Filter */}
            {allColors.length > 0 && (
              <div className="border-b border-gray-200">
                <button
                  onClick={() => toggleFilterSection('color')}
                  className="w-full p-4 text-left font-medium text-[#000907] flex items-center justify-between hover:bg-gray-50"
                >
                  <span className="flex items-center">
                    <span className="w-4 h-4 mr-2 bg-gradient-to-r from-pink-500 via-purple-500 to-blue-500 rounded-full"></span>
                    Colors
                    {selectedColors.length > 0 && (
                      <span className="ml-2 text-xs bg-[#000907] text-white px-1.5 py-0.5 rounded">
                        {selectedColors.length}
                      </span>
                    )}
                  </span>
                  {activeFilterSection === 'color' ?
                    <ChevronUp className="h-4 w-4" /> :
                    <ChevronDown className="h-4 w-4" />
                  }
                </button>

                <div
                  className={`px-4 pb-4 ${
                    activeFilterSection === 'color' ? 'block' : 'hidden'
                  }`}
                >
                  <div className="grid grid-cols-3 gap-2">
                    {allColors.map(color => {
                      // Generate a background color based on the color name
                      let bgColor = '';
                      switch(color.toLowerCase()) {
                        case 'black': bgColor = 'bg-black'; break;
                        case 'white': bgColor = 'bg-white'; break;
                        case 'red': bgColor = 'bg-red-500'; break;
                        case 'blue': bgColor = 'bg-blue-500'; break;
                        case 'pink': bgColor = 'bg-pink-400'; break;
                        case 'beige': bgColor = 'bg-amber-100'; break;
                        case 'green': bgColor = 'bg-green-500'; break;
                        case 'yellow': bgColor = 'bg-yellow-400'; break;
                        case 'purple': bgColor = 'bg-purple-500'; break;
                        case 'floral': bgColor = 'bg-gradient-to-r from-pink-300 via-purple-300 to-indigo-300'; break;
                        default: bgColor = 'bg-gray-300';
                      }

                      return (
                        <button
                          key={color}
                          onClick={() => handleColorToggle(color)}
                          className={`flex flex-col items-center p-2 ${
                            selectedColors.includes(color)
                              ? 'bg-gray-100'
                              : ''
                          }`}
                        >
                          <span className={`w-6 h-6 rounded-full ${bgColor} mb-1 ${
                            selectedColors.includes(color) ? 'ring-2 ring-[#000907]' : ''
                          } ${color.toLowerCase() === 'white' ? 'border border-gray-200' : ''}`}></span>
                          <span className="text-xs">{color}</span>
                        </button>
                      );
                    })}
                  </div>
                </div>
              </div>
            )}

            {/* Size Filter */}
            {allSizes.length > 0 && (
              <div className="border-b border-gray-200">
                <button
                  onClick={() => toggleFilterSection('size')}
                  className="w-full p-4 text-left font-medium text-[#000907] flex items-center justify-between hover:bg-gray-50"
                >
                  <span className="flex items-center">
                    <Tag className="h-4 w-4 mr-2" />
                    Sizes
                    {selectedSizes.length > 0 && (
                      <span className="ml-2 text-xs bg-[#000907] text-white px-1.5 py-0.5 rounded">
                        {selectedSizes.length}
                      </span>
                    )}
                  </span>
                  {activeFilterSection === 'size' ?
                    <ChevronUp className="h-4 w-4" /> :
                    <ChevronDown className="h-4 w-4" />
                  }
                </button>

                <div
                  className={`px-4 pb-4 ${
                    activeFilterSection === 'size' ? 'block' : 'hidden'
                  }`}
                >
                  <div className="grid grid-cols-4 gap-2">
                    {allSizes.map(size => (
                      <button
                        key={size}
                        onClick={() => handleSizeToggle(size)}
                        className={`h-9 flex items-center justify-center border ${
                          selectedSizes.includes(size)
                            ? 'border-[#000907] bg-[#000907] text-white'
                            : 'border-gray-300 hover:border-[#000907]'
                        }`}
                      >
                        {size}
                      </button>
                    ))}
                  </div>
                  <div className="mt-3 text-xs text-gray-500">
                    <p>Select multiple sizes if needed</p>
                  </div>
                </div>
              </div>
            )}





            {/* Material Filter */}
            {allMaterials.length > 0 && (
              <div className="border-b border-gray-200">
                <button
                  onClick={() => toggleFilterSection('material')}
                  className="w-full p-4 text-left font-medium text-[#000907] flex items-center justify-between hover:bg-gray-50"
                >
                  <span className="flex items-center">
                    <span className="mr-2 text-[#000907]">🧵</span>
                    Materials
                    {selectedMaterials.length > 0 && (
                      <span className="ml-2 text-xs bg-[#000907] text-white px-1.5 py-0.5 rounded">
                        {selectedMaterials.length}
                      </span>
                    )}
                  </span>
                  {activeFilterSection === 'material' ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
                </button>

                {activeFilterSection === 'material' && (
                  <div className="px-4 pb-4">
                    <div className="flex flex-wrap gap-2">
                      {allMaterials.map(material => (
                        <button
                          key={material}
                          onClick={() => handleMaterialToggle(material)}
                          className={`px-3 py-1.5 text-xs rounded-full transition-all ${
                            selectedMaterials.includes(material)
                              ? 'bg-[#000907] text-white'
                              : 'bg-gray-100 text-[#000907] hover:bg-gray-200 border border-gray-200'
                          }`}
                        >
                          {material}
                        </button>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            )}

            {/* Style Filter */}
            {allStyles.length > 0 && (
              <div className="border-b border-gray-200">
                <button
                  onClick={() => toggleFilterSection('style')}
                  className="w-full p-4 text-left font-medium text-[#000907] flex items-center justify-between hover:bg-gray-50"
                >
                  <span className="flex items-center">
                    <span className="mr-2 text-[#000907]">👗</span>
                    Styles
                    {selectedStyles.length > 0 && (
                      <span className="ml-2 text-xs bg-[#000907] text-white px-1.5 py-0.5 rounded">
                        {selectedStyles.length}
                      </span>
                    )}
                  </span>
                  {activeFilterSection === 'style' ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
                </button>

                {activeFilterSection === 'style' && (
                  <div className="px-4 pb-4">
                    <div className="flex flex-wrap gap-2">
                      {allStyles.map(style => (
                        <button
                          key={style}
                          onClick={() => handleStyleToggle(style)}
                          className={`px-3 py-1.5 text-xs rounded-full transition-all ${
                            selectedStyles.includes(style)
                              ? 'bg-[#000907] text-white'
                              : 'bg-gray-100 text-[#000907] hover:bg-gray-200 border border-gray-200'
                          }`}
                        >
                          {style}
                        </button>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            )}

            {/* Occasion Filter */}
            {allOccasions.length > 0 && (
              <div className="border-b border-gray-200">
                <button
                  onClick={() => toggleFilterSection('occasion')}
                  className="w-full p-4 text-left font-medium text-[#000907] flex items-center justify-between hover:bg-gray-50"
                >
                  <span className="flex items-center">
                    <span className="mr-2 text-[#000907]">🎉</span>
                    Occasions
                    {selectedOccasions.length > 0 && (
                      <span className="ml-2 text-xs bg-[#000907] text-white px-1.5 py-0.5 rounded">
                        {selectedOccasions.length}
                      </span>
                    )}
                  </span>
                  {activeFilterSection === 'occasion' ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
                </button>

                {activeFilterSection === 'occasion' && (
                  <div className="px-4 pb-4">
                    <div className="flex flex-wrap gap-2">
                      {allOccasions.map(occasion => (
                        <button
                          key={occasion}
                          onClick={() => handleOccasionToggle(occasion)}
                          className={`px-3 py-1.5 text-xs rounded-full transition-all ${
                            selectedOccasions.includes(occasion)
                              ? 'bg-[#000907] text-white'
                              : 'bg-gray-100 text-[#000907] hover:bg-gray-200 border border-gray-200'
                          }`}
                        >
                          {occasion}
                        </button>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            )}

            {/* Season Filter */}
            {allSeasons.length > 0 && (
              <div className="border-b border-gray-200">
                <button
                  onClick={() => toggleFilterSection('season')}
                  className="w-full p-4 text-left font-medium text-[#000907] flex items-center justify-between hover:bg-gray-50"
                >
                  <span className="flex items-center">
                    <span className="mr-2 text-[#000907]">🍂</span>
                    Seasons
                    {selectedSeasons.length > 0 && (
                      <span className="ml-2 text-xs bg-[#000907] text-white px-1.5 py-0.5 rounded">
                        {selectedSeasons.length}
                      </span>
                    )}
                  </span>
                  {activeFilterSection === 'season' ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
                </button>

                {activeFilterSection === 'season' && (
                  <div className="px-4 pb-4">
                    <div className="flex flex-wrap gap-2">
                      {allSeasons.map(season => (
                        <button
                          key={season}
                          onClick={() => handleSeasonToggle(season)}
                          className={`px-3 py-1.5 text-xs rounded-full transition-all ${
                            selectedSeasons.includes(season)
                              ? 'bg-[#000907] text-white'
                              : 'bg-gray-100 text-[#000907] hover:bg-gray-200 border border-gray-200'
                          }`}
                        >
                          {season}
                        </button>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            )}
            {/* Tags Filter */}
            {allTags.length > 0 && (
              <div className="border-b border-[#f3e8e8]">
                <button
                  onClick={() => toggleFilterSection('tags')}
                  className="w-full p-4 text-left font-medium text-[#6d3d3d] flex items-center justify-between hover:bg-[#f9f5f5] transition-colors"
                >
                  <span className="flex items-center">
                    <Tag className="h-4 w-4 mr-2" />
                    Tags
                    {selectedTags.length > 0 && (
                      <span className="ml-2 text-xs bg-[#6d3d3d] text-white px-1.5 py-0.5 rounded-full">
                        {selectedTags.length}
                      </span>
                    )}
                  </span>
                  {activeFilterSection === 'tags' ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
                </button>

                {activeFilterSection === 'tags' && (
                  <div className="px-4 pb-4">
                    <div className="flex flex-wrap gap-2">
                      {allTags.map(tag => (
                        <button
                          key={tag}
                          onClick={() => handleTagToggle(tag)}
                          className={`px-3 py-1.5 text-xs rounded-full transition-all ${
                            selectedTags.includes(tag)
                              ? 'bg-[#6d3d3d] text-white'
                              : 'bg-[#f9f5f5] text-[#6d3d3d] hover:bg-[#f3e8e8] border border-[#e9dada]'
                          }`}
                        >
                          #{tag}
                        </button>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            )}

            {/* Filter panel footer */}
            <div className="p-5 bg-gradient-to-b from-[#fdfafa] to-[#f9f5f5] border-t border-[#f3e8e8]">
              <div className="flex flex-col gap-3">
                {activeFilterCount > 0 ? (
                  <>
                    <div className="flex items-center justify-between">
                      <p className="text-sm text-gray-600 flex items-center">
                        <Filter className="h-3.5 w-3.5 mr-1.5 text-[#6d3d3d]" />
                        <span className="font-medium text-[#6d3d3d]">{activeFilterCount}</span> active filters
                      </p>
                      <span className="text-xs text-[#6d3d3d] bg-[#f3e8e8] px-2 py-0.5 rounded-full">
                        {filteredAndSortedProducts.length} results
                      </span>
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={clearAllFilters}
                      className="w-full border-[#e9dada] text-[#d83f31] hover:text-[#b82a1e] hover:bg-[#ffeeee] transition-all duration-200 group"
                    >
                      <X className="h-3.5 w-3.5 mr-1.5 group-hover:rotate-90 transition-transform duration-200" />
                      Clear All Filters
                    </Button>
                  </>
                ) : (
                  <div className="bg-white p-3 rounded-lg shadow-sm border border-[#f3e8e8]">
                    <p className="text-sm text-gray-500 flex items-start">
                      <span className="text-[#6d3d3d] mr-2">💡</span>
                      No filters applied. Select filters to refine your search.
                    </p>
                  </div>
                )}

                {/* Filter display options removed */}
              </div>
            </div>
          </div>

          {/* Active filters display (mobile only) */}
          {activeFilterCount > 0 && (
            <div className="mt-4 md:hidden">
              <div className="bg-white p-4 rounded-xl shadow-md border border-[#f3e8e8]">
                <div className="flex items-center justify-between mb-3">
                  <h3 className="text-sm font-medium text-[#6d3d3d] flex items-center">
                    <Filter className="h-3.5 w-3.5 mr-1.5" />
                    Active Filters
                    <span className="ml-1.5 bg-[#6d3d3d] text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                      {activeFilterCount}
                    </span>
                  </h3>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={clearAllFilters}
                    className="text-xs text-[#d83f31] hover:text-[#b82a1e] hover:bg-[#ffeeee] h-7 px-2 rounded-full"
                  >
                    <X className="h-3.5 w-3.5 mr-1" />
                    Clear All
                  </Button>
                </div>
                <div className="flex flex-wrap gap-2">
                  {selectedColors.map(color => {
                    // Generate a background color based on the color name
                    let bgColor = '';
                    switch(color.toLowerCase()) {
                      case 'black': bgColor = 'bg-black'; break;
                      case 'white': bgColor = 'bg-white'; break;
                      case 'red': bgColor = 'bg-red-500'; break;
                      case 'blue': bgColor = 'bg-blue-500'; break;
                      case 'pink': bgColor = 'bg-pink-400'; break;
                      case 'beige': bgColor = 'bg-amber-100'; break;
                      case 'green': bgColor = 'bg-green-500'; break;
                      case 'yellow': bgColor = 'bg-yellow-400'; break;
                      case 'purple': bgColor = 'bg-purple-500'; break;
                      case 'floral': bgColor = 'bg-gradient-to-r from-pink-300 via-purple-300 to-indigo-300'; break;
                      default: bgColor = 'bg-gray-300';
                    }

                    return (
                      <div key={color} className="bg-[#f9f5f5] text-[#6d3d3d] text-xs px-2 py-1 rounded-full flex items-center shadow-sm border border-[#f3e8e8]">
                        <span className={`w-3 h-3 rounded-full ${bgColor} mr-1 ${color.toLowerCase() === 'white' ? 'border border-gray-200' : ''}`}></span>
                        {color}
                        <button
                          onClick={() => handleColorToggle(color)}
                          className="ml-1 bg-white rounded-full p-0.5 hover:bg-[#f3e8e8] transition-colors"
                        >
                          <X className="h-2.5 w-2.5" />
                        </button>
                      </div>
                    );
                  })}
                  {selectedSizes.map(size => (
                    <div key={size} className="bg-[#f9f5f5] text-[#6d3d3d] text-xs px-2 py-1 rounded-full flex items-center shadow-sm border border-[#f3e8e8]">
                      <Tag className="h-3 w-3 mr-1" />
                      {size}
                      <button
                        onClick={() => handleSizeToggle(size)}
                        className="ml-1 bg-white rounded-full p-0.5 hover:bg-[#f3e8e8] transition-colors"
                      >
                        <X className="h-2.5 w-2.5" />
                      </button>
                    </div>
                  ))}

                  {showOnSale && (
                    <div className="bg-[#f9f5f5] text-[#6d3d3d] text-xs px-2 py-1 rounded-full flex items-center shadow-sm border border-[#f3e8e8]">
                      <Percent className="h-3 w-3 mr-1" />
                      On Sale
                      <button
                        onClick={() => setShowOnSale(false)}
                        className="ml-1 bg-white rounded-full p-0.5 hover:bg-[#f3e8e8] transition-colors"
                      >
                        <X className="h-2.5 w-2.5" />
                      </button>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Products grid */}
        <div className="products-container flex-1">
          {/* Category title and description - centered at the top */}
          <div className="mb-8 text-center">
            <h1 className="text-xl sm:text-2xl md:text-3xl font-bold text-[#000907] mb-2 sm:mb-3 uppercase">
              {searchParams.get('search')
                ? `SEARCH RESULTS FOR "${searchParams.get('search')}"`
                : subcategory?.toUpperCase() || category?.toUpperCase() || 'ALL PRODUCTS'
              }
            </h1>
            <p className="text-sm sm:text-base text-gray-600 max-w-2xl mx-auto px-2">
              {getCategoryDescription(category, subcategory)}
            </p>
          </div>

          {/* Breadcrumb and sort controls */}
          <div className="mb-6 flex flex-col sm:flex-row justify-between items-start sm:items-center gap-3">
            {/* Breadcrumb navigation - right aligned on desktop */}
            <nav className="hidden sm:flex items-center text-sm text-gray-500 overflow-x-auto whitespace-nowrap">
              <Link to="/" className="flex items-center hover:text-[#000907] transition-colors">
                <Home className="h-3.5 w-3.5 mr-1" />
                <span>Home</span>
              </Link>
              <ChevronRight className="h-3.5 w-3.5 mx-2" />

              {category && typeof category === 'string' && category.length > 0 && (
                <>
                  <Link to="/category" className="hover:text-[#6d3d3d] transition-colors">
                    Categories
                  </Link>
                  <ChevronRight className="h-3.5 w-3.5 mx-2" />
                  <Link to={`/category/${category}`} className="hover:text-[#6d3d3d] transition-colors">
                    {category.charAt(0).toUpperCase() + category.slice(1)}
                  </Link>
                  {subcategory && typeof subcategory === 'string' && subcategory.length > 0 && (
                    <>
                      <ChevronRight className="h-3.5 w-3.5 mx-2" />
                      <span className="text-[#6d3d3d] font-medium">
                        {subcategory.charAt(0).toUpperCase() + subcategory.slice(1)}
                      </span>
                    </>
                  )}
                </>
              )}

              {!category && !subcategory && (
                <span className="text-[#6d3d3d] font-medium">
                  {searchParams.get('search')
                    ? `Search Results`
                    : 'All Products'
                  }
                </span>
              )}
            </nav>

            <div className="flex items-center ml-auto">
              <div className="text-sm text-gray-600 mr-4">
                <span className="font-medium text-[#6d3d3d]">
                  {filteredAndSortedProducts.length > 0
                    ? `${Math.min((currentPage - 1) * productsPerPage + 1, filteredAndSortedProducts.length)}-${Math.min(currentPage * productsPerPage, filteredAndSortedProducts.length)} of ${filteredAndSortedProducts.length}`
                    : '0'
                  }
                </span> products
              </div>

              <ArrowUpDown className="h-4 w-4 text-[#6d3d3d] mr-2" />
              <select
                className="text-sm bg-transparent border border-gray-300 p-1 text-[#6d3d3d]"
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
              >
                <option value="newest">Newest Arrivals</option>
                <option value="price-asc">Price: Low to High</option>
                <option value="price-desc">Price: High to Low</option>
                <option value="rating">Highest Rated</option>
                <option value="bestselling">Best Selling</option>
                <option value="discount">Biggest Discount</option>
              </select>
            </div>
          </div>

          {/* Products */}
          {filteredAndSortedProducts.length === 0 ? (
            <div className="text-center py-12 bg-white border border-gray-200">
              <div className="flex flex-col items-center">
                <ShoppingBag className="h-10 w-10 text-[#6d3d3d] mb-4" />
                <h3 className="text-lg font-medium text-[#6d3d3d] mb-2">No products found</h3>
                <p className="text-gray-500 max-w-md px-4">
                  We couldn't find any products matching your current filter criteria. Try adjusting your filters or browse our other collections.
                </p>
                <Button
                  variant="outline"
                  className="mt-4 border-gray-300 text-[#6d3d3d]"
                  onClick={clearAllFilters}
                >
                  Clear All Filters
                </Button>
              </div>
            </div>
          ) : (
            <>
              <div
                ref={productsContainerRef}
                className="products-grid grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 xl:grid-cols-4 gap-3 sm:gap-4"
              >
                {/* Show only current page products */}
                {filteredAndSortedProducts
                  .slice((currentPage - 1) * productsPerPage, currentPage * productsPerPage)
                  .map((product) => {
                    const convertedProduct = convertProductForUI(product);
                    return (
                      <div key={product.product_id} className="h-full">
                        <CardTypeThree
                          image={convertedProduct.image}
                          name={convertedProduct.name}
                          price={convertedProduct.price}
                          discountPrice={convertedProduct.discountPrice}
                          link={`/product/${product.slug}`}
                          isNew={convertedProduct.isNew}
                          category={convertedProduct.category}
                          rating={convertedProduct.rating}
                          reviewCount={product.rating.reviews_count}
                          originalPrice={convertedProduct.discountPrice ? `₹${product.price.original.toFixed(0)}` : undefined}
                          productId={product.product_id}
                          discount={product.price.discount_percentage ? `${product.price.discount_percentage}%` : undefined}
                          tags={convertedProduct.isNew ? ["New"] : []}
                          onQuickView={() => {
                            setQuickViewProduct(product);
                            setIsQuickViewOpen(true);
                          }}
                        />
                      </div>
                    );
                  })}
              </div>

              {/* Simplified pagination showing all numbers */}
              {totalPages > 1 && (
                <div className="mt-8 flex justify-center">
                  <div className="flex items-center gap-1 flex-wrap justify-center">
                    {/* Previous page button */}
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-8 w-8 p-0"
                      onClick={() => currentPage > 1 && handlePageChange(currentPage - 1)}
                      disabled={currentPage === 1}
                    >
                      <ChevronLeft className="h-4 w-4" />
                    </Button>

                    {/* Show all page numbers */}
                    {Array.from({ length: totalPages }, (_, i) => {
                      const pageNum = i + 1;
                      return (
                        <Button
                          key={pageNum}
                          variant={currentPage === pageNum ? "default" : "ghost"}
                          size="sm"
                          className={`h-8 w-8 p-0 ${currentPage === pageNum ? 'bg-[#000907] hover:bg-[#1D1616]' : ''}`}
                          onClick={() => handlePageChange(pageNum)}
                        >
                          <span>{pageNum}</span>
                        </Button>
                      );
                    })}

                    {/* Next page button */}
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-8 w-8 p-0"
                      onClick={() => currentPage < totalPages && handlePageChange(currentPage + 1)}
                      disabled={currentPage === totalPages}
                    >
                      <ChevronRight className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              )}
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default ProductsPage;
