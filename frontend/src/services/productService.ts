import { apiService, API_ENDPOINTS } from './apiService';

export interface ProductFilters {
  category?: string;
  subcategory?: string;
  brand?: string;
  minPrice?: number;
  maxPrice?: number;
  inStock?: boolean;
  minRating?: number;
  material?: string;
  style?: string;
  occasion?: string;
  season?: string;
  search?: string;
  sortBy?: 'price_low_to_high' | 'price_high_to_low' | 'rating' | 'newest' | 'popularity' | 'discount';
  page?: number;
  limit?: number;
}

export interface ProductSearchParams {
  q: string;
  category?: string;
  subcategory?: string;
  brand?: string;
  minPrice?: number;
  maxPrice?: number;
  inStock?: boolean;
  minRating?: number;
  sortBy?: string;
  page?: number;
  limit?: number;
}

class ProductService {
  // Get all products with filtering and pagination (matches backend /api/products)
  async getAllProducts(filters: ProductFilters = {}) {
    const params = new URLSearchParams();

    // Map frontend filters to backend parameters exactly as backend expects
    if (filters.category) params.append('category', filters.category);
    if (filters.subcategory) params.append('subcategory', filters.subcategory);
    if (filters.brand) params.append('brand', filters.brand);
    if (filters.minPrice) params.append('minPrice', filters.minPrice.toString());
    if (filters.maxPrice) params.append('maxPrice', filters.maxPrice.toString());
    if (filters.inStock !== undefined) params.append('inStock', filters.inStock.toString());
    if (filters.minRating) params.append('minRating', filters.minRating.toString());
    if (filters.material) params.append('material', filters.material);
    if (filters.style) params.append('style', filters.style);
    if (filters.occasion) params.append('occasion', filters.occasion);
    if (filters.season) params.append('season', filters.season);
    if (filters.search) params.append('search', filters.search);
    if (filters.sortBy) params.append('sortBy', filters.sortBy);
    if (filters.page) params.append('page', filters.page.toString());
    if (filters.limit) params.append('limit', filters.limit.toString());

    const response = await apiService.get(`/products?${params.toString()}`);
    return response.data;
  }

  // Get featured products
  async getFeaturedProducts(limit: number = 8) {
    const response = await apiService.get(`/products/featured?limit=${limit}`);
    return response.data;
  }

  // Get bestseller products
  async getBestsellerProducts(limit: number = 8) {
    const response = await apiService.get(`/products/bestsellers?limit=${limit}`);
    return response.data;
  }

  // Get new arrival products
  async getNewArrivalProducts(limit: number = 8) {
    const response = await apiService.get(`/products/new-arrivals?limit=${limit}`);
    return response.data;
  }

  // Get trending products
  async getTrendingProducts(limit: number = 8) {
    const response = await apiService.get(`/products/trending?limit=${limit}`);
    return response.data;
  }

  // Get product categories
  async getCategories() {
    const response = await apiService.get('/products/categories');
    return response.data;
  }

  // Get products by category (matches backend /api/products/category/:categoryId)
  async getProductsByCategory(categoryName: string, options: { page?: number; limit?: number; sortBy?: string } = {}) {
    const params = new URLSearchParams();
    if (options.page) params.append('page', options.page.toString());
    if (options.limit) params.append('limit', options.limit.toString());
    if (options.sortBy) params.append('sortBy', options.sortBy);

    const response = await apiService.get(`/products/category/${encodeURIComponent(categoryName)}?${params.toString()}`);
    return response.data;
  }

  // Search products (matches backend /api/products/search)
  async searchProducts(searchParams: ProductSearchParams) {
    const params = new URLSearchParams();

    // Backend expects 'q' parameter for search term
    if (searchParams.q) params.append('q', searchParams.q);
    if (searchParams.page) params.append('page', searchParams.page.toString());
    if (searchParams.limit) params.append('limit', searchParams.limit.toString());
    if (searchParams.sortBy) params.append('sortBy', searchParams.sortBy);

    const response = await apiService.get(`/products/search?${params.toString()}`);
    return response.data;
  }

  // Get product by ID
  async getProductById(id: string) {
    const response = await apiService.get(`/products/${id}`);
    return response.data;
  }

  // Get product by slug
  async getProductBySlug(slug: string) {
    const response = await apiService.get(`/products/slug/${slug}`);
    return response.data;
  }

  // Get product recommendations (enhanced with relationship types)
  async getRecommendations(id: string, limit: number = 4) {
    const response = await apiService.get(`/products/${id}/recommendations?limit=${limit}`);
    return response.data;
  }

  // Get related products with relationship types
  async getRelatedProducts(id: string, type?: 'similar' | 'complementary' | 'alternative' | 'accessory' | 'complete_look', limit: number = 6) {
    const params = new URLSearchParams();
    if (type) params.append('type', type);
    params.append('limit', limit.toString());

    const response = await apiService.get(`/products/${id}/related?${params.toString()}`);
    return response.data;
  }

  // Get product gallery images
  async getProductGallery(id: string) {
    const response = await apiService.get(`/products/${id}/gallery`);
    return response.data;
  }

  // Get product reviews
  async getProductReviews(id: string, page: number = 1, limit: number = 10) {
    const response = await apiService.get(`/products/${id}/reviews?page=${page}&limit=${limit}`);
    return response.data;
  }

  // Get product variants
  async getProductVariants(id: string) {
    const response = await apiService.get(`/products/${id}/variants`);
    return response.data;
  }

  // Get product statistics
  async getProductStats() {
    const response = await apiService.get('/products/stats');
    return response.data;
  }

  // Utility methods for frontend
  formatPrice(price: number, currency: string = 'USD'): string {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
    }).format(price);
  }

  calculateDiscount(originalPrice: number, currentPrice: number): number {
    if (originalPrice <= currentPrice) return 0;
    return Math.round(((originalPrice - currentPrice) / originalPrice) * 100);
  }

  getProductImageUrl(product: any, index: number = 0): string {
    if (product.product_images && product.product_images.length > 0) {
      return product.product_images[index]?.image_url || product.product_images[0]?.image_url;
    }
    return '/images/placeholder-product.jpg'; // Fallback image
  }

  isProductInStock(product: any): boolean {
    return product.availability === 'In Stock' && product.inventory?.available_stock > 0;
  }

  getStockStatus(product: any): string {
    if (!product.inventory?.track_inventory) return 'In Stock';

    const stock = product.inventory.available_stock || 0;
    const threshold = product.inventory.low_stock_threshold || 10;

    if (stock === 0) return 'Out of Stock';
    if (stock <= threshold) return 'Low Stock';
    return 'In Stock';
  }

  getStockStatusColor(status: string): string {
    switch (status) {
      case 'In Stock': return 'text-green-600';
      case 'Low Stock': return 'text-yellow-600';
      case 'Out of Stock': return 'text-red-600';
      default: return 'text-gray-600';
    }
  }

  // Filter helpers for frontend
  getUniqueValues(products: any[], field: string): any[] {
    const values = products.map(product => {
      if (field.includes('.')) {
        return field.split('.').reduce((obj, key) => obj?.[key], product);
      }
      return product[field];
    }).filter(Boolean);

    return [...new Set(values)].sort();
  }

  getPriceRange(products: any[]): { min: number; max: number } {
    const prices = products.map(p => p.price?.current || 0).filter(p => p > 0);
    return {
      min: Math.min(...prices),
      max: Math.max(...prices)
    };
  }
}

export const productService = new ProductService();
